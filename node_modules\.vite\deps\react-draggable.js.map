{"version": 3, "sources": ["../../react-is/cjs/react-is.development.js", "../../react-is/index.js", "../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../clsx/dist/clsx.js", "../../react-draggable/build/cjs/utils/shims.js", "../../react-draggable/build/cjs/utils/getPrefix.js", "../../react-draggable/build/cjs/utils/domFns.js", "../../react-draggable/build/cjs/utils/positionFns.js", "../../react-draggable/build/cjs/utils/log.js", "../../react-draggable/build/cjs/DraggableCore.js", "../../react-draggable/build/cjs/Draggable.js", "../../react-draggable/build/cjs/cjs.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "function r(e){var o,t,f=\"\";if(\"string\"==typeof e||\"number\"==typeof e)f+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(t=r(e[o]))&&(f&&(f+=\" \"),f+=t)}else for(t in e)e[t]&&(f&&(f+=\" \"),f+=t);return f}function e(){for(var e,o,t=0,f=\"\",n=arguments.length;t<n;t++)(e=arguments[t])&&(o=r(e))&&(f&&(f+=\" \"),f+=o);return f}module.exports=e,module.exports.clsx=e;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dontSetMe = dontSetMe;\nexports.findInArray = findInArray;\nexports.int = int;\nexports.isFunction = isFunction;\nexports.isNum = isNum;\n// @credits https://gist.github.com/rogozhnikoff/a43cfed27c41e4e68cdc\nfunction findInArray(array /*: Array<any> | TouchList*/, callback /*: Function*/) /*: any*/{\n  for (let i = 0, length = array.length; i < length; i++) {\n    if (callback.apply(callback, [array[i], i, array])) return array[i];\n  }\n}\nfunction isFunction(func /*: any*/) /*: boolean %checks*/{\n  // $FlowIgnore[method-unbinding]\n  return typeof func === 'function' || Object.prototype.toString.call(func) === '[object Function]';\n}\nfunction isNum(num /*: any*/) /*: boolean %checks*/{\n  return typeof num === 'number' && !isNaN(num);\n}\nfunction int(a /*: string*/) /*: number*/{\n  return parseInt(a, 10);\n}\nfunction dontSetMe(props /*: Object*/, propName /*: string*/, componentName /*: string*/) /*: ?Error*/{\n  if (props[propName]) {\n    return new Error(`Invalid prop ${propName} passed to ${componentName} - do not set this, set it on the child.`);\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.browserPrefixToKey = browserPrefixToKey;\nexports.browserPrefixToStyle = browserPrefixToStyle;\nexports.default = void 0;\nexports.getPrefix = getPrefix;\nconst prefixes = ['Moz', 'Webkit', 'O', 'ms'];\nfunction getPrefix() /*: string*/{\n  let prop /*: string*/ = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'transform';\n  // Ensure we're running in an environment where there is actually a global\n  // `window` obj\n  if (typeof window === 'undefined') return '';\n\n  // If we're in a pseudo-browser server-side environment, this access\n  // path may not exist, so bail out if it doesn't.\n  const style = window.document?.documentElement?.style;\n  if (!style) return '';\n  if (prop in style) return '';\n  for (let i = 0; i < prefixes.length; i++) {\n    if (browserPrefixToKey(prop, prefixes[i]) in style) return prefixes[i];\n  }\n  return '';\n}\nfunction browserPrefixToKey(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? `${prefix}${kebabToTitleCase(prop)}` : prop;\n}\nfunction browserPrefixToStyle(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? `-${prefix.toLowerCase()}-${prop}` : prop;\n}\nfunction kebabToTitleCase(str /*: string*/) /*: string*/{\n  let out = '';\n  let shouldCapitalize = true;\n  for (let i = 0; i < str.length; i++) {\n    if (shouldCapitalize) {\n      out += str[i].toUpperCase();\n      shouldCapitalize = false;\n    } else if (str[i] === '-') {\n      shouldCapitalize = true;\n    } else {\n      out += str[i];\n    }\n  }\n  return out;\n}\n\n// Default export is the prefix itself, like 'Moz', 'Webkit', etc\n// Note that you may have to re-test for certain things; for instance, Chrome 50\n// can handle unprefixed `transform`, but not unprefixed `user-select`\nvar _default = exports.default = (getPrefix() /*: string*/);", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addClassName = addClassName;\nexports.addEvent = addEvent;\nexports.addUserSelectStyles = addUserSelectStyles;\nexports.createCSSTransform = createCSSTransform;\nexports.createSVGTransform = createSVGTransform;\nexports.getTouch = getTouch;\nexports.getTouchIdentifier = getTouchIdentifier;\nexports.getTranslation = getTranslation;\nexports.innerHeight = innerHeight;\nexports.innerWidth = innerWidth;\nexports.matchesSelector = matchesSelector;\nexports.matchesSelectorAndParentsTo = matchesSelectorAndParentsTo;\nexports.offsetXYFromParent = offsetXYFromParent;\nexports.outerHeight = outerHeight;\nexports.outerWidth = outerWidth;\nexports.removeClassName = removeClassName;\nexports.removeEvent = removeEvent;\nexports.scheduleRemoveUserSelectStyles = scheduleRemoveUserSelectStyles;\nvar _shims = require(\"./shims\");\nvar _getPrefix = _interopRequireWildcard(require(\"./getPrefix\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n/*:: import type {ControlPosition, PositionOffsetControlPosition, MouseTouchEvent} from './types';*/\nlet matchesSelectorFunc = '';\nfunction matchesSelector(el /*: Node*/, selector /*: string*/) /*: boolean*/{\n  if (!matchesSelectorFunc) {\n    matchesSelectorFunc = (0, _shims.findInArray)(['matches', 'webkitMatchesSelector', 'mozMatchesSelector', 'msMatchesSelector', 'oMatchesSelector'], function (method) {\n      // $FlowIgnore: Doesn't think elements are indexable\n      return (0, _shims.isFunction)(el[method]);\n    });\n  }\n\n  // Might not be found entirely (not an Element?) - in that case, bail\n  // $FlowIgnore: Doesn't think elements are indexable\n  if (!(0, _shims.isFunction)(el[matchesSelectorFunc])) return false;\n\n  // $FlowIgnore: Doesn't think elements are indexable\n  return el[matchesSelectorFunc](selector);\n}\n\n// Works up the tree to the draggable itself attempting to match selector.\nfunction matchesSelectorAndParentsTo(el /*: Node*/, selector /*: string*/, baseNode /*: Node*/) /*: boolean*/{\n  let node = el;\n  do {\n    if (matchesSelector(node, selector)) return true;\n    if (node === baseNode) return false;\n    // $FlowIgnore[incompatible-type]\n    node = node.parentNode;\n  } while (node);\n  return false;\n}\nfunction addEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.addEventListener) {\n    el.addEventListener(event, handler, options);\n  } else if (el.attachEvent) {\n    el.attachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = handler;\n  }\n}\nfunction removeEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.removeEventListener) {\n    el.removeEventListener(event, handler, options);\n  } else if (el.detachEvent) {\n    el.detachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = null;\n  }\n}\nfunction outerHeight(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetTop which is including margin. See getBoundPosition\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height += (0, _shims.int)(computedStyle.borderTopWidth);\n  height += (0, _shims.int)(computedStyle.borderBottomWidth);\n  return height;\n}\nfunction outerWidth(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetLeft which is including margin. See getBoundPosition\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width += (0, _shims.int)(computedStyle.borderLeftWidth);\n  width += (0, _shims.int)(computedStyle.borderRightWidth);\n  return width;\n}\nfunction innerHeight(node /*: HTMLElement*/) /*: number*/{\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height -= (0, _shims.int)(computedStyle.paddingTop);\n  height -= (0, _shims.int)(computedStyle.paddingBottom);\n  return height;\n}\nfunction innerWidth(node /*: HTMLElement*/) /*: number*/{\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width -= (0, _shims.int)(computedStyle.paddingLeft);\n  width -= (0, _shims.int)(computedStyle.paddingRight);\n  return width;\n}\n/*:: interface EventWithOffset {\n  clientX: number, clientY: number\n}*/\n// Get from offsetParent\nfunction offsetXYFromParent(evt /*: EventWithOffset*/, offsetParent /*: HTMLElement*/, scale /*: number*/) /*: ControlPosition*/{\n  const isBody = offsetParent === offsetParent.ownerDocument.body;\n  const offsetParentRect = isBody ? {\n    left: 0,\n    top: 0\n  } : offsetParent.getBoundingClientRect();\n  const x = (evt.clientX + offsetParent.scrollLeft - offsetParentRect.left) / scale;\n  const y = (evt.clientY + offsetParent.scrollTop - offsetParentRect.top) / scale;\n  return {\n    x,\n    y\n  };\n}\nfunction createCSSTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: Object*/{\n  const translation = getTranslation(controlPos, positionOffset, 'px');\n  return {\n    [(0, _getPrefix.browserPrefixToKey)('transform', _getPrefix.default)]: translation\n  };\n}\nfunction createSVGTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: string*/{\n  const translation = getTranslation(controlPos, positionOffset, '');\n  return translation;\n}\nfunction getTranslation(_ref /*:: */, positionOffset /*: PositionOffsetControlPosition*/, unitSuffix /*: string*/) /*: string*/{\n  let {\n    x,\n    y\n  } /*: ControlPosition*/ = _ref /*: ControlPosition*/;\n  let translation = `translate(${x}${unitSuffix},${y}${unitSuffix})`;\n  if (positionOffset) {\n    const defaultX = `${typeof positionOffset.x === 'string' ? positionOffset.x : positionOffset.x + unitSuffix}`;\n    const defaultY = `${typeof positionOffset.y === 'string' ? positionOffset.y : positionOffset.y + unitSuffix}`;\n    translation = `translate(${defaultX}, ${defaultY})` + translation;\n  }\n  return translation;\n}\nfunction getTouch(e /*: MouseTouchEvent*/, identifier /*: number*/) /*: ?{clientX: number, clientY: number}*/{\n  return e.targetTouches && (0, _shims.findInArray)(e.targetTouches, t => identifier === t.identifier) || e.changedTouches && (0, _shims.findInArray)(e.changedTouches, t => identifier === t.identifier);\n}\nfunction getTouchIdentifier(e /*: MouseTouchEvent*/) /*: ?number*/{\n  if (e.targetTouches && e.targetTouches[0]) return e.targetTouches[0].identifier;\n  if (e.changedTouches && e.changedTouches[0]) return e.changedTouches[0].identifier;\n}\n\n// User-select Hacks:\n//\n// Useful for preventing blue highlights all over everything when dragging.\n\n// Note we're passing `document` b/c we could be iframed\nfunction addUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  let styleEl = doc.getElementById('react-draggable-style-el');\n  if (!styleEl) {\n    styleEl = doc.createElement('style');\n    styleEl.type = 'text/css';\n    styleEl.id = 'react-draggable-style-el';\n    styleEl.innerHTML = '.react-draggable-transparent-selection *::-moz-selection {all: inherit;}\\n';\n    styleEl.innerHTML += '.react-draggable-transparent-selection *::selection {all: inherit;}\\n';\n    doc.getElementsByTagName('head')[0].appendChild(styleEl);\n  }\n  if (doc.body) addClassName(doc.body, 'react-draggable-transparent-selection');\n}\nfunction scheduleRemoveUserSelectStyles(doc /*: ?Document*/) {\n  // Prevent a possible \"forced reflow\"\n  if (window.requestAnimationFrame) {\n    window.requestAnimationFrame(() => {\n      removeUserSelectStyles(doc);\n    });\n  } else {\n    removeUserSelectStyles(doc);\n  }\n}\nfunction removeUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  try {\n    if (doc.body) removeClassName(doc.body, 'react-draggable-transparent-selection');\n    // $FlowIgnore: IE\n    if (doc.selection) {\n      // $FlowIgnore: IE\n      doc.selection.empty();\n    } else {\n      // Remove selection caused by scroll, unless it's a focused input\n      // (we use doc.defaultView in case we're in an iframe)\n      const selection = (doc.defaultView || window).getSelection();\n      if (selection && selection.type !== 'Caret') {\n        selection.removeAllRanges();\n      }\n    }\n  } catch (e) {\n    // probably IE\n  }\n}\nfunction addClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.add(className);\n  } else {\n    if (!el.className.match(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`))) {\n      el.className += ` ${className}`;\n    }\n  }\n}\nfunction removeClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.remove(className);\n  } else {\n    el.className = el.className.replace(new RegExp(`(?:^|\\\\s)${className}(?!\\\\S)`, 'g'), '');\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.canDragX = canDragX;\nexports.canDragY = canDragY;\nexports.createCoreData = createCoreData;\nexports.createDraggableData = createDraggableData;\nexports.getBoundPosition = getBoundPosition;\nexports.getControlPosition = getControlPosition;\nexports.snapToGrid = snapToGrid;\nvar _shims = require(\"./shims\");\nvar _domFns = require(\"./domFns\");\n/*:: import type Draggable from '../Draggable';*/\n/*:: import type {Bounds, ControlPosition, DraggableData, MouseTouchEvent} from './types';*/\n/*:: import type DraggableCore from '../DraggableCore';*/\nfunction getBoundPosition(draggable /*: Draggable*/, x /*: number*/, y /*: number*/) /*: [number, number]*/{\n  // If no bounds, short-circuit and move on\n  if (!draggable.props.bounds) return [x, y];\n\n  // Clone new bounds\n  let {\n    bounds\n  } = draggable.props;\n  bounds = typeof bounds === 'string' ? bounds : cloneBounds(bounds);\n  const node = findDOMNode(draggable);\n  if (typeof bounds === 'string') {\n    const {\n      ownerDocument\n    } = node;\n    const ownerWindow = ownerDocument.defaultView;\n    let boundNode;\n    if (bounds === 'parent') {\n      boundNode = node.parentNode;\n    } else {\n      // Flow assigns the wrong return type (Node) for getRootNode(),\n      // so we cast it to one of the correct types (Element).\n      // The others are Document and ShadowRoot.\n      // All three implement querySelector() so it's safe to call.\n      const rootNode = ((node.getRootNode() /*: any*/) /*: Element*/);\n      boundNode = rootNode.querySelector(bounds);\n    }\n    if (!(boundNode instanceof ownerWindow.HTMLElement)) {\n      throw new Error('Bounds selector \"' + bounds + '\" could not find an element.');\n    }\n    const boundNodeEl /*: HTMLElement*/ = boundNode; // for Flow, can't seem to refine correctly\n    const nodeStyle = ownerWindow.getComputedStyle(node);\n    const boundNodeStyle = ownerWindow.getComputedStyle(boundNodeEl);\n    // Compute bounds. This is a pain with padding and offsets but this gets it exactly right.\n    bounds = {\n      left: -node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingLeft) + (0, _shims.int)(nodeStyle.marginLeft),\n      top: -node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingTop) + (0, _shims.int)(nodeStyle.marginTop),\n      right: (0, _domFns.innerWidth)(boundNodeEl) - (0, _domFns.outerWidth)(node) - node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingRight) - (0, _shims.int)(nodeStyle.marginRight),\n      bottom: (0, _domFns.innerHeight)(boundNodeEl) - (0, _domFns.outerHeight)(node) - node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingBottom) - (0, _shims.int)(nodeStyle.marginBottom)\n    };\n  }\n\n  // Keep x and y below right and bottom limits...\n  if ((0, _shims.isNum)(bounds.right)) x = Math.min(x, bounds.right);\n  if ((0, _shims.isNum)(bounds.bottom)) y = Math.min(y, bounds.bottom);\n\n  // But above left and top limits.\n  if ((0, _shims.isNum)(bounds.left)) x = Math.max(x, bounds.left);\n  if ((0, _shims.isNum)(bounds.top)) y = Math.max(y, bounds.top);\n  return [x, y];\n}\nfunction snapToGrid(grid /*: [number, number]*/, pendingX /*: number*/, pendingY /*: number*/) /*: [number, number]*/{\n  const x = Math.round(pendingX / grid[0]) * grid[0];\n  const y = Math.round(pendingY / grid[1]) * grid[1];\n  return [x, y];\n}\nfunction canDragX(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'x';\n}\nfunction canDragY(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'y';\n}\n\n// Get {x, y} positions from event.\nfunction getControlPosition(e /*: MouseTouchEvent*/, touchIdentifier /*: ?number*/, draggableCore /*: DraggableCore*/) /*: ?ControlPosition*/{\n  const touchObj = typeof touchIdentifier === 'number' ? (0, _domFns.getTouch)(e, touchIdentifier) : null;\n  if (typeof touchIdentifier === 'number' && !touchObj) return null; // not the right touch\n  const node = findDOMNode(draggableCore);\n  // User can provide an offsetParent if desired.\n  const offsetParent = draggableCore.props.offsetParent || node.offsetParent || node.ownerDocument.body;\n  return (0, _domFns.offsetXYFromParent)(touchObj || e, offsetParent, draggableCore.props.scale);\n}\n\n// Create an data object exposed by <DraggableCore>'s events\nfunction createCoreData(draggable /*: DraggableCore*/, x /*: number*/, y /*: number*/) /*: DraggableData*/{\n  const isStart = !(0, _shims.isNum)(draggable.lastX);\n  const node = findDOMNode(draggable);\n  if (isStart) {\n    // If this is our first move, use the x and y as last coords.\n    return {\n      node,\n      deltaX: 0,\n      deltaY: 0,\n      lastX: x,\n      lastY: y,\n      x,\n      y\n    };\n  } else {\n    // Otherwise calculate proper values.\n    return {\n      node,\n      deltaX: x - draggable.lastX,\n      deltaY: y - draggable.lastY,\n      lastX: draggable.lastX,\n      lastY: draggable.lastY,\n      x,\n      y\n    };\n  }\n}\n\n// Create an data exposed by <Draggable>'s events\nfunction createDraggableData(draggable /*: Draggable*/, coreData /*: DraggableData*/) /*: DraggableData*/{\n  const scale = draggable.props.scale;\n  return {\n    node: coreData.node,\n    x: draggable.state.x + coreData.deltaX / scale,\n    y: draggable.state.y + coreData.deltaY / scale,\n    deltaX: coreData.deltaX / scale,\n    deltaY: coreData.deltaY / scale,\n    lastX: draggable.state.x,\n    lastY: draggable.state.y\n  };\n}\n\n// A lot faster than stringify/parse\nfunction cloneBounds(bounds /*: Bounds*/) /*: Bounds*/{\n  return {\n    left: bounds.left,\n    top: bounds.top,\n    right: bounds.right,\n    bottom: bounds.bottom\n  };\n}\nfunction findDOMNode(draggable /*: Draggable | DraggableCore*/) /*: HTMLElement*/{\n  const node = draggable.findDOMNode();\n  if (!node) {\n    throw new Error('<DraggableCore>: Unmounted during event!');\n  }\n  // $FlowIgnore we can't assert on HTMLElement due to tests... FIXME\n  return node;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = log;\n/*eslint no-console:0*/\nfunction log() {\n  if (undefined) console.log(...arguments);\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/*:: import type {EventHandler, MouseTouchEvent} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n// Simple abstraction for dragging events names.\nconst eventsFor = {\n  touch: {\n    start: 'touchstart',\n    move: 'touchmove',\n    stop: 'touchend'\n  },\n  mouse: {\n    start: 'mousedown',\n    move: 'mousemove',\n    stop: 'mouseup'\n  }\n};\n\n// Default to mouse events.\nlet dragEventFor = eventsFor.mouse;\n/*:: export type DraggableData = {\n  node: HTMLElement,\n  x: number, y: number,\n  deltaX: number, deltaY: number,\n  lastX: number, lastY: number,\n};*/\n/*:: export type DraggableEventHandler = (e: MouseEvent, data: DraggableData) => void | false;*/\n/*:: export type ControlPosition = {x: number, y: number};*/\n/*:: export type PositionOffsetControlPosition = {x: number|string, y: number|string};*/\n/*:: export type DraggableCoreDefaultProps = {\n  allowAnyClick: boolean,\n  allowMobileScroll: boolean,\n  disabled: boolean,\n  enableUserSelectHack: boolean,\n  onStart: DraggableEventHandler,\n  onDrag: DraggableEventHandler,\n  onStop: DraggableEventHandler,\n  onMouseDown: (e: MouseEvent) => void,\n  scale: number,\n};*/\n/*:: export type DraggableCoreProps = {\n  ...DraggableCoreDefaultProps,\n  cancel: string,\n  children: ReactElement<any>,\n  offsetParent: HTMLElement,\n  grid: [number, number],\n  handle: string,\n  nodeRef?: ?React.ElementRef<any>,\n};*/\n//\n// Define <DraggableCore>.\n//\n// <DraggableCore> is for advanced usage of <Draggable>. It maintains minimal internal state so it can\n// work well with libraries that require more control over the element.\n//\n\nclass DraggableCore extends React.Component /*:: <DraggableCoreProps>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"dragging\", false);\n    // Used while dragging to determine deltas.\n    _defineProperty(this, \"lastX\", NaN);\n    _defineProperty(this, \"lastY\", NaN);\n    _defineProperty(this, \"touchIdentifier\", null);\n    _defineProperty(this, \"mounted\", false);\n    _defineProperty(this, \"handleDragStart\", e => {\n      // Make it possible to attach event handlers on top of this one.\n      this.props.onMouseDown(e);\n\n      // Only accept left-clicks.\n      if (!this.props.allowAnyClick && typeof e.button === 'number' && e.button !== 0) return false;\n\n      // Get nodes. Be sure to grab relative document (could be iframed)\n      const thisNode = this.findDOMNode();\n      if (!thisNode || !thisNode.ownerDocument || !thisNode.ownerDocument.body) {\n        throw new Error('<DraggableCore> not mounted on DragStart!');\n      }\n      const {\n        ownerDocument\n      } = thisNode;\n\n      // Short circuit if handle or cancel prop was provided and selector doesn't match.\n      if (this.props.disabled || !(e.target instanceof ownerDocument.defaultView.Node) || this.props.handle && !(0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.handle, thisNode) || this.props.cancel && (0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.cancel, thisNode)) {\n        return;\n      }\n\n      // Prevent scrolling on mobile devices, like ipad/iphone.\n      // Important that this is after handle/cancel.\n      if (e.type === 'touchstart' && !this.props.allowMobileScroll) e.preventDefault();\n\n      // Set touch identifier in component state if this is a touch event. This allows us to\n      // distinguish between individual touches on multitouch screens by identifying which\n      // touchpoint was set to this element.\n      const touchIdentifier = (0, _domFns.getTouchIdentifier)(e);\n      this.touchIdentifier = touchIdentifier;\n\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, touchIdentifier, this);\n      if (position == null) return; // not possible but satisfies flow\n      const {\n        x,\n        y\n      } = position;\n\n      // Create an event object with all the data parents need to make a decision here.\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDragStart: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, cancel.\n      (0, _log.default)('calling', this.props.onStart);\n      const shouldUpdate = this.props.onStart(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) return;\n\n      // Add a style to the body to disable user-select. This prevents text from\n      // being selected all over the page.\n      if (this.props.enableUserSelectHack) (0, _domFns.addUserSelectStyles)(ownerDocument);\n\n      // Initiate dragging. Set the current x and y as offsets\n      // so we know how much we've moved during the drag. This allows us\n      // to drag elements around even if they have been moved, without issue.\n      this.dragging = true;\n      this.lastX = x;\n      this.lastY = y;\n\n      // Add events to the document directly so we catch when the user's mouse/touch moves outside of\n      // this element. We use different events depending on whether or not we have detected that this\n      // is a touch-capable device.\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.move, this.handleDrag);\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.stop, this.handleDragStop);\n    });\n    _defineProperty(this, \"handleDrag\", e => {\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX,\n          deltaY = y - this.lastY;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        if (!deltaX && !deltaY) return; // skip useless drag\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDrag: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, trigger end.\n      const shouldUpdate = this.props.onDrag(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) {\n        try {\n          // $FlowIgnore\n          this.handleDragStop(new MouseEvent('mouseup'));\n        } catch (err) {\n          // Old browsers\n          const event = ((document.createEvent('MouseEvents') /*: any*/) /*: MouseTouchEvent*/);\n          // I see why this insanity was deprecated\n          // $FlowIgnore\n          event.initMouseEvent('mouseup', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n          this.handleDragStop(event);\n        }\n        return;\n      }\n      this.lastX = x;\n      this.lastY = y;\n    });\n    _defineProperty(this, \"handleDragStop\", e => {\n      if (!this.dragging) return;\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX || 0;\n        let deltaY = y - this.lastY || 0;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n\n      // Call event handler\n      const shouldContinue = this.props.onStop(e, coreEvent);\n      if (shouldContinue === false || this.mounted === false) return false;\n      const thisNode = this.findDOMNode();\n      if (thisNode) {\n        // Remove user-select hack\n        if (this.props.enableUserSelectHack) (0, _domFns.scheduleRemoveUserSelectStyles)(thisNode.ownerDocument);\n      }\n      (0, _log.default)('DraggableCore: handleDragStop: %j', coreEvent);\n\n      // Reset the el.\n      this.dragging = false;\n      this.lastX = NaN;\n      this.lastY = NaN;\n      if (thisNode) {\n        // Remove event handlers\n        (0, _log.default)('DraggableCore: Removing handlers');\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.move, this.handleDrag);\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.stop, this.handleDragStop);\n      }\n    });\n    _defineProperty(this, \"onMouseDown\", e => {\n      dragEventFor = eventsFor.mouse; // on touchscreen laptops we could switch back to mouse\n\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onMouseUp\", e => {\n      dragEventFor = eventsFor.mouse;\n      return this.handleDragStop(e);\n    });\n    // Same as onMouseDown (start drag), but now consider this a touch device.\n    _defineProperty(this, \"onTouchStart\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onTouchEnd\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStop(e);\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    // Touch handlers must be added with {passive: false} to be cancelable.\n    // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      (0, _domFns.addEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n    // Remove any leftover event handlers. Remove both touch and mouse handlers in case\n    // some browser quirk caused a touch event to fire during a mouse move, or vice versa.\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      const {\n        ownerDocument\n      } = thisNode;\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n      if (this.props.enableUserSelectHack) (0, _domFns.scheduleRemoveUserSelectStyles)(ownerDocument);\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    return this.props?.nodeRef ? this.props?.nodeRef?.current : _reactDom.default.findDOMNode(this);\n  }\n  render() /*: React.Element<any>*/{\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.cloneElement(React.Children.only(this.props.children), {\n      // Note: mouseMove handler is attached to document so it will still function\n      // when the user drags quickly and leaves the bounds of the element.\n      onMouseDown: this.onMouseDown,\n      onMouseUp: this.onMouseUp,\n      // onTouchStart is added on `componentDidMount` so they can be added with\n      // {passive: false}, which allows it to cancel. See\n      // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n      onTouchEnd: this.onTouchEnd\n    });\n  }\n}\nexports.default = DraggableCore;\n_defineProperty(DraggableCore, \"displayName\", 'DraggableCore');\n_defineProperty(DraggableCore, \"propTypes\", {\n  /**\n   * `allowAnyClick` allows dragging using any mouse button.\n   * By default, we only accept the left button.\n   *\n   * Defaults to `false`.\n   */\n  allowAnyClick: _propTypes.default.bool,\n  /**\n   * `allowMobileScroll` turns off cancellation of the 'touchstart' event\n   * on mobile devices. Only enable this if you are having trouble with click\n   * events. Prefer using 'handle' / 'cancel' instead.\n   *\n   * Defaults to `false`.\n   */\n  allowMobileScroll: _propTypes.default.bool,\n  children: _propTypes.default.node.isRequired,\n  /**\n   * `disabled`, if true, stops the <Draggable> from dragging. All handlers,\n   * with the exception of `onMouseDown`, will not fire.\n   */\n  disabled: _propTypes.default.bool,\n  /**\n   * By default, we add 'user-select:none' attributes to the document body\n   * to prevent ugly text selection during drag. If this is causing problems\n   * for your app, set this to `false`.\n   */\n  enableUserSelectHack: _propTypes.default.bool,\n  /**\n   * `offsetParent`, if set, uses the passed DOM node to compute drag offsets\n   * instead of using the parent node.\n   */\n  offsetParent: function (props /*: DraggableCoreProps*/, propName /*: $Keys<DraggableCoreProps>*/) {\n    if (props[propName] && props[propName].nodeType !== 1) {\n      throw new Error('Draggable\\'s offsetParent must be a DOM Node.');\n    }\n  },\n  /**\n   * `grid` specifies the x and y that dragging should snap to.\n   */\n  grid: _propTypes.default.arrayOf(_propTypes.default.number),\n  /**\n   * `handle` specifies a selector to be used as the handle that initiates drag.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable handle=\".handle\">\n   *              <div>\n   *                  <div className=\"handle\">Click me to drag</div>\n   *                  <div>This is some other content</div>\n   *              </div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  handle: _propTypes.default.string,\n  /**\n   * `cancel` specifies a selector to be used to prevent drag initialization.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *           return(\n   *               <Draggable cancel=\".cancel\">\n   *                   <div>\n   *                     <div className=\"cancel\">You can't drag from here</div>\n   *                     <div>Dragging here works fine</div>\n   *                   </div>\n   *               </Draggable>\n   *           );\n   *       }\n   *   });\n   * ```\n   */\n  cancel: _propTypes.default.string,\n  /* If running in React Strict mode, ReactDOM.findDOMNode() is deprecated.\n   * Unfortunately, in order for <Draggable> to work properly, we need raw access\n   * to the underlying DOM node. If you want to avoid the warning, pass a `nodeRef`\n   * as in this example:\n   *\n   * function MyComponent() {\n   *   const nodeRef = React.useRef(null);\n   *   return (\n   *     <Draggable nodeRef={nodeRef}>\n   *       <div ref={nodeRef}>Example Target</div>\n   *     </Draggable>\n   *   );\n   * }\n   *\n   * This can be used for arbitrarily nested components, so long as the ref ends up\n   * pointing to the actual child DOM node and not a custom component.\n   */\n  nodeRef: _propTypes.default.object,\n  /**\n   * Called when dragging starts.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onStart: _propTypes.default.func,\n  /**\n   * Called while dragging.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onDrag: _propTypes.default.func,\n  /**\n   * Called when dragging stops.\n   * If this function returns the boolean false, the drag will remain active.\n   */\n  onStop: _propTypes.default.func,\n  /**\n   * A workaround option which can be passed if onMouseDown needs to be accessed,\n   * since it'll always be blocked (as there is internal use of onMouseDown)\n   */\n  onMouseDown: _propTypes.default.func,\n  /**\n   * `scale`, if set, applies scaling while dragging an element\n   */\n  scale: _propTypes.default.number,\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(DraggableCore, \"defaultProps\", {\n  allowAnyClick: false,\n  // by default only accept left click\n  allowMobileScroll: false,\n  disabled: false,\n  enableUserSelectHack: true,\n  onStart: function () {},\n  onDrag: function () {},\n  onStop: function () {},\n  onMouseDown: function () {},\n  scale: 1\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"DraggableCore\", {\n  enumerable: true,\n  get: function () {\n    return _DraggableCore.default;\n  }\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _clsx = require(\"clsx\");\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _DraggableCore = _interopRequireDefault(require(\"./DraggableCore\"));\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } /*:: import type {ControlPosition, PositionOffsetControlPosition, DraggableCoreProps, DraggableCoreDefaultProps} from './DraggableCore';*/\n/*:: import type {Bounds, DraggableEventHandler} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n/*:: type DraggableState = {\n  dragging: boolean,\n  dragged: boolean,\n  x: number, y: number,\n  slackX: number, slackY: number,\n  isElementSVG: boolean,\n  prevPropsPosition: ?ControlPosition,\n};*/\n/*:: export type DraggableDefaultProps = {\n  ...DraggableCoreDefaultProps,\n  axis: 'both' | 'x' | 'y' | 'none',\n  bounds: Bounds | string | false,\n  defaultClassName: string,\n  defaultClassNameDragging: string,\n  defaultClassNameDragged: string,\n  defaultPosition: ControlPosition,\n  scale: number,\n};*/\n/*:: export type DraggableProps = {\n  ...DraggableCoreProps,\n  ...DraggableDefaultProps,\n  positionOffset: PositionOffsetControlPosition,\n  position: ControlPosition,\n};*/\n//\n// Define <Draggable>\n//\nclass Draggable extends React.Component /*:: <DraggableProps, DraggableState>*/{\n  // React 16.3+\n  // Arity (props, state)\n  static getDerivedStateFromProps(_ref /*:: */, _ref2 /*:: */) /*: ?Partial<DraggableState>*/{\n    let {\n      position\n    } /*: DraggableProps*/ = _ref /*: DraggableProps*/;\n    let {\n      prevPropsPosition\n    } /*: DraggableState*/ = _ref2 /*: DraggableState*/;\n    // Set x/y if a new position is provided in props that is different than the previous.\n    if (position && (!prevPropsPosition || position.x !== prevPropsPosition.x || position.y !== prevPropsPosition.y)) {\n      (0, _log.default)('Draggable: getDerivedStateFromProps %j', {\n        position,\n        prevPropsPosition\n      });\n      return {\n        x: position.x,\n        y: position.y,\n        prevPropsPosition: {\n          ...position\n        }\n      };\n    }\n    return null;\n  }\n  constructor(props /*: DraggableProps*/) {\n    super(props);\n    _defineProperty(this, \"onDragStart\", (e, coreData) => {\n      (0, _log.default)('Draggable: onDragStart: %j', coreData);\n\n      // Short-circuit if user's callback killed it.\n      const shouldStart = this.props.onStart(e, (0, _positionFns.createDraggableData)(this, coreData));\n      // Kills start event on core as well, so move handlers are never bound.\n      if (shouldStart === false) return false;\n      this.setState({\n        dragging: true,\n        dragged: true\n      });\n    });\n    _defineProperty(this, \"onDrag\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n      (0, _log.default)('Draggable: onDrag: %j', coreData);\n      const uiData = (0, _positionFns.createDraggableData)(this, coreData);\n      const newState = {\n        x: uiData.x,\n        y: uiData.y,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // Keep within bounds.\n      if (this.props.bounds) {\n        // Save original x and y.\n        const {\n          x,\n          y\n        } = newState;\n\n        // Add slack to the values used to calculate bound position. This will ensure that if\n        // we start removing slack, the element won't react to it right away until it's been\n        // completely removed.\n        newState.x += this.state.slackX;\n        newState.y += this.state.slackY;\n\n        // Get bound position. This will ceil/floor the x and y within the boundaries.\n        const [newStateX, newStateY] = (0, _positionFns.getBoundPosition)(this, newState.x, newState.y);\n        newState.x = newStateX;\n        newState.y = newStateY;\n\n        // Recalculate slack by noting how much was shaved by the boundPosition handler.\n        newState.slackX = this.state.slackX + (x - newState.x);\n        newState.slackY = this.state.slackY + (y - newState.y);\n\n        // Update the event we fire to reflect what really happened after bounds took effect.\n        uiData.x = newState.x;\n        uiData.y = newState.y;\n        uiData.deltaX = newState.x - this.state.x;\n        uiData.deltaY = newState.y - this.state.y;\n      }\n\n      // Short-circuit if user's callback killed it.\n      const shouldUpdate = this.props.onDrag(e, uiData);\n      if (shouldUpdate === false) return false;\n      this.setState(newState);\n    });\n    _defineProperty(this, \"onDragStop\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n\n      // Short-circuit if user's callback killed it.\n      const shouldContinue = this.props.onStop(e, (0, _positionFns.createDraggableData)(this, coreData));\n      if (shouldContinue === false) return false;\n      (0, _log.default)('Draggable: onDragStop: %j', coreData);\n      const newState /*: Partial<DraggableState>*/ = {\n        dragging: false,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // If this is a controlled component, the result of this operation will be to\n      // revert back to the old position. We expect a handler on `onDragStop`, at the least.\n      const controlled = Boolean(this.props.position);\n      if (controlled) {\n        const {\n          x,\n          y\n        } = this.props.position;\n        newState.x = x;\n        newState.y = y;\n      }\n      this.setState(newState);\n    });\n    this.state = {\n      // Whether or not we are currently dragging.\n      dragging: false,\n      // Whether or not we have been dragged before.\n      dragged: false,\n      // Current transform x and y.\n      x: props.position ? props.position.x : props.defaultPosition.x,\n      y: props.position ? props.position.y : props.defaultPosition.y,\n      prevPropsPosition: {\n        ...props.position\n      },\n      // Used for compensating for out-of-bounds drags\n      slackX: 0,\n      slackY: 0,\n      // Can only determine if SVG after mounting\n      isElementSVG: false\n    };\n    if (props.position && !(props.onDrag || props.onStop)) {\n      // eslint-disable-next-line no-console\n      console.warn('A `position` was applied to this <Draggable>, without drag handlers. This will make this ' + 'component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the ' + '`position` of this element.');\n    }\n  }\n  componentDidMount() {\n    // Check to see if the element passed is an instanceof SVGElement\n    if (typeof window.SVGElement !== 'undefined' && this.findDOMNode() instanceof window.SVGElement) {\n      this.setState({\n        isElementSVG: true\n      });\n    }\n  }\n  componentWillUnmount() {\n    if (this.state.dragging) {\n      this.setState({\n        dragging: false\n      }); // prevents invariant if unmounted while dragging\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    return this.props?.nodeRef?.current ?? _reactDom.default.findDOMNode(this);\n  }\n  render() /*: ReactElement<any>*/{\n    const {\n      axis,\n      bounds,\n      children,\n      defaultPosition,\n      defaultClassName,\n      defaultClassNameDragging,\n      defaultClassNameDragged,\n      position,\n      positionOffset,\n      scale,\n      ...draggableCoreProps\n    } = this.props;\n    let style = {};\n    let svgTransform = null;\n\n    // If this is controlled, we don't want to move it - unless it's dragging.\n    const controlled = Boolean(position);\n    const draggable = !controlled || this.state.dragging;\n    const validPosition = position || defaultPosition;\n    const transformOpts = {\n      // Set left if horizontal drag is enabled\n      x: (0, _positionFns.canDragX)(this) && draggable ? this.state.x : validPosition.x,\n      // Set top if vertical drag is enabled\n      y: (0, _positionFns.canDragY)(this) && draggable ? this.state.y : validPosition.y\n    };\n\n    // If this element was SVG, we use the `transform` attribute.\n    if (this.state.isElementSVG) {\n      svgTransform = (0, _domFns.createSVGTransform)(transformOpts, positionOffset);\n    } else {\n      // Add a CSS transform to move the element around. This allows us to move the element around\n      // without worrying about whether or not it is relatively or absolutely positioned.\n      // If the item you are dragging already has a transform set, wrap it in a <span> so <Draggable>\n      // has a clean slate.\n      style = (0, _domFns.createCSSTransform)(transformOpts, positionOffset);\n    }\n\n    // Mark with class while dragging\n    const className = (0, _clsx.clsx)(children.props.className || '', defaultClassName, {\n      [defaultClassNameDragging]: this.state.dragging,\n      [defaultClassNameDragged]: this.state.dragged\n    });\n\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.createElement(_DraggableCore.default, _extends({}, draggableCoreProps, {\n      onStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onStop: this.onDragStop\n    }), /*#__PURE__*/React.cloneElement(React.Children.only(children), {\n      className: className,\n      style: {\n        ...children.props.style,\n        ...style\n      },\n      transform: svgTransform\n    }));\n  }\n}\nexports.default = Draggable;\n_defineProperty(Draggable, \"displayName\", 'Draggable');\n_defineProperty(Draggable, \"propTypes\", {\n  // Accepts all props <DraggableCore> accepts.\n  ..._DraggableCore.default.propTypes,\n  /**\n   * `axis` determines which axis the draggable can move.\n   *\n   *  Note that all callbacks will still return data as normal. This only\n   *  controls flushing to the DOM.\n   *\n   * 'both' allows movement horizontally and vertically.\n   * 'x' limits movement to horizontal axis.\n   * 'y' limits movement to vertical axis.\n   * 'none' limits all movement.\n   *\n   * Defaults to 'both'.\n   */\n  axis: _propTypes.default.oneOf(['both', 'x', 'y', 'none']),\n  /**\n   * `bounds` determines the range of movement available to the element.\n   * Available values are:\n   *\n   * 'parent' restricts movement within the Draggable's parent node.\n   *\n   * Alternatively, pass an object with the following properties, all of which are optional:\n   *\n   * {left: LEFT_BOUND, right: RIGHT_BOUND, bottom: BOTTOM_BOUND, top: TOP_BOUND}\n   *\n   * All values are in px.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable bounds={{right: 300, bottom: 300}}>\n   *              <div>Content</div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  bounds: _propTypes.default.oneOfType([_propTypes.default.shape({\n    left: _propTypes.default.number,\n    right: _propTypes.default.number,\n    top: _propTypes.default.number,\n    bottom: _propTypes.default.number\n  }), _propTypes.default.string, _propTypes.default.oneOf([false])]),\n  defaultClassName: _propTypes.default.string,\n  defaultClassNameDragging: _propTypes.default.string,\n  defaultClassNameDragged: _propTypes.default.string,\n  /**\n   * `defaultPosition` specifies the x and y that the dragged item should start at\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable defaultPosition={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  defaultPosition: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  positionOffset: _propTypes.default.shape({\n    x: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string]),\n    y: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string])\n  }),\n  /**\n   * `position`, if present, defines the current position of the element.\n   *\n   *  This is similar to how form elements in React work - if no `position` is supplied, the component\n   *  is uncontrolled.\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable position={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  position: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(Draggable, \"defaultProps\", {\n  ..._DraggableCore.default.defaultProps,\n  axis: 'both',\n  bounds: false,\n  defaultClassName: 'react-draggable',\n  defaultClassNameDragging: 'react-draggable-dragging',\n  defaultClassNameDragged: 'react-draggable-dragged',\n  defaultPosition: {\n    x: 0,\n    y: 0\n  },\n  scale: 1\n});", "\"use strict\";\n\nconst {\n  default: Draggable,\n  DraggableCore\n} = require('./Draggable');\n\n// Previous versions of this lib exported <Draggable> as the root export. As to no-// them, or TypeScript, we export *both* as the root and as 'default'.\n// See https://github.com/mzabriskie/react-draggable/pull/254\n// and https://github.com/mzabriskie/react-draggable/issues/266\nmodule.exports = Draggable;\nmodule.exports.default = Draggable;\nmodule.exports.DraggableCore = DraggableCore;"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,gBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAAS,GAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASA,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;ACZN;AAAA;AAAA,aAAS,EAAEC,IAAE;AAAC,UAAI,GAAE,GAAE,IAAE;AAAG,UAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,GAAE,MAAGA;AAAA,eAAU,YAAU,OAAOA,GAAE,KAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,YAAI,IAAEA,GAAE;AAAO,aAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAAA,GAAE,CAAC,MAAI,IAAE,EAAEA,GAAE,CAAC,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAA,MAAE,MAAM,MAAI,KAAKA,GAAE,CAAAA,GAAE,CAAC,MAAI,MAAI,KAAG,MAAK,KAAG;AAAG,aAAO;AAAA,IAAC;AAAC,aAAS,IAAG;AAAC,eAAQA,IAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,QAAO,IAAE,GAAE,IAAI,EAACA,KAAE,UAAU,CAAC,OAAK,IAAE,EAAEA,EAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAG,aAAO;AAAA,IAAC;AAAC,WAAO,UAAQ,GAAE,OAAO,QAAQ,OAAK;AAAA;AAAA;;;ACA3Y;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,YAAY;AACpB,YAAQ,cAAc;AACtB,YAAQ,MAAM;AACd,YAAQ,aAAa;AACrB,YAAQ,QAAQ;AAEhB,aAAS,YAAY,OAAoC,UAAkC;AACzF,eAAS,IAAI,GAAG,SAAS,MAAM,QAAQ,IAAI,QAAQ,KAAK;AACtD,YAAI,SAAS,MAAM,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,EAAG,QAAO,MAAM,CAAC;AAAA,MACpE;AAAA,IACF;AACA,aAAS,WAAW,MAAqC;AAEvD,aAAO,OAAO,SAAS,cAAc,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AAAA,IAChF;AACA,aAAS,MAAM,KAAoC;AACjD,aAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,GAAG;AAAA,IAC9C;AACA,aAAS,IAAI,GAA4B;AACvC,aAAO,SAAS,GAAG,EAAE;AAAA,IACvB;AACA,aAAS,UAAU,OAAoB,UAAuB,eAAwC;AACpG,UAAI,MAAM,QAAQ,GAAG;AACnB,eAAO,IAAI,MAAM,gBAAgB,QAAQ,cAAc,aAAa,0CAA0C;AAAA,MAChH;AAAA,IACF;AAAA;AAAA;;;AC9BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,qBAAqB;AAC7B,YAAQ,uBAAuB;AAC/B,YAAQ,UAAU;AAClB,YAAQ,YAAY;AACpB,QAAM,WAAW,CAAC,OAAO,UAAU,KAAK,IAAI;AAC5C,aAAS,YAAwB;AAC/B,UAAI,OAAoB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAG5F,UAAI,OAAO,WAAW,YAAa,QAAO;AAI1C,YAAM,QAAQ,OAAO,UAAU,iBAAiB;AAChD,UAAI,CAAC,MAAO,QAAO;AACnB,UAAI,QAAQ,MAAO,QAAO;AAC1B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,mBAAmB,MAAM,SAAS,CAAC,CAAC,KAAK,MAAO,QAAO,SAAS,CAAC;AAAA,MACvE;AACA,aAAO;AAAA,IACT;AACA,aAAS,mBAAmB,MAAmB,QAAiC;AAC9E,aAAO,SAAS,GAAG,MAAM,GAAG,iBAAiB,IAAI,CAAC,KAAK;AAAA,IACzD;AACA,aAAS,qBAAqB,MAAmB,QAAiC;AAChF,aAAO,SAAS,IAAI,OAAO,YAAY,CAAC,IAAI,IAAI,KAAK;AAAA,IACvD;AACA,aAAS,iBAAiB,KAA8B;AACtD,UAAI,MAAM;AACV,UAAI,mBAAmB;AACvB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,kBAAkB;AACpB,iBAAO,IAAI,CAAC,EAAE,YAAY;AAC1B,6BAAmB;AAAA,QACrB,WAAW,IAAI,CAAC,MAAM,KAAK;AACzB,6BAAmB;AAAA,QACrB,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,QAAI,WAAW,QAAQ,UAAW,UAAU;AAAA;AAAA;;;ACnD5C;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,YAAQ,WAAW;AACnB,YAAQ,sBAAsB;AAC9B,YAAQ,qBAAqB;AAC7B,YAAQ,qBAAqB;AAC7B,YAAQ,WAAW;AACnB,YAAQ,qBAAqB;AAC7B,YAAQ,iBAAiB;AACzB,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,8BAA8B;AACtC,YAAQ,qBAAqB;AAC7B,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,cAAc;AACtB,YAAQ,iCAAiC;AACzC,QAAI,SAAS;AACb,QAAI,aAAa,wBAAwB,mBAAsB;AAC/D,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AAErmB,QAAI,sBAAsB;AAC1B,aAAS,gBAAgB,IAAe,UAAoC;AAC1E,UAAI,CAAC,qBAAqB;AACxB,+BAAuB,GAAG,OAAO,aAAa,CAAC,WAAW,yBAAyB,sBAAsB,qBAAqB,kBAAkB,GAAG,SAAU,QAAQ;AAEnK,kBAAQ,GAAG,OAAO,YAAY,GAAG,MAAM,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AAIA,UAAI,EAAE,GAAG,OAAO,YAAY,GAAG,mBAAmB,CAAC,EAAG,QAAO;AAG7D,aAAO,GAAG,mBAAmB,EAAE,QAAQ;AAAA,IACzC;AAGA,aAAS,4BAA4B,IAAe,UAAuB,UAAkC;AAC3G,UAAI,OAAO;AACX,SAAG;AACD,YAAI,gBAAgB,MAAM,QAAQ,EAAG,QAAO;AAC5C,YAAI,SAAS,SAAU,QAAO;AAE9B,eAAO,KAAK;AAAA,MACd,SAAS;AACT,aAAO;AAAA,IACT;AACA,aAAS,SAAS,IAAgB,OAAoB,SAAwB,cAAqC;AACjH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,kBAAkB;AACvB,WAAG,iBAAiB,OAAO,SAAS,OAAO;AAAA,MAC7C,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,IAAgB,OAAoB,SAAwB,cAAqC;AACpH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,qBAAqB;AAC1B,WAAG,oBAAoB,OAAO,SAAS,OAAO;AAAA,MAChD,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,MAAoC;AAGvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,cAAc;AACtD,iBAAW,GAAG,OAAO,KAAK,cAAc,iBAAiB;AACzD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AAGtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,eAAe;AACtD,gBAAU,GAAG,OAAO,KAAK,cAAc,gBAAgB;AACvD,aAAO;AAAA,IACT;AACA,aAAS,YAAY,MAAoC;AACvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,UAAU;AAClD,iBAAW,GAAG,OAAO,KAAK,cAAc,aAAa;AACrD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AACtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,WAAW;AAClD,gBAAU,GAAG,OAAO,KAAK,cAAc,YAAY;AACnD,aAAO;AAAA,IACT;AAKA,aAAS,mBAAmB,KAA2B,cAAgC,OAAyC;AAC9H,YAAM,SAAS,iBAAiB,aAAa,cAAc;AAC3D,YAAM,mBAAmB,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,KAAK;AAAA,MACP,IAAI,aAAa,sBAAsB;AACvC,YAAM,KAAK,IAAI,UAAU,aAAa,aAAa,iBAAiB,QAAQ;AAC5E,YAAM,KAAK,IAAI,UAAU,aAAa,YAAY,iBAAiB,OAAO;AAC1E,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,IAAI;AACnE,aAAO;AAAA,QACL,EAAE,GAAG,WAAW,oBAAoB,aAAa,WAAW,OAAO,CAAC,GAAG;AAAA,MACzE;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,EAAE;AACjE,aAAO;AAAA,IACT;AACA,aAAS,eAAe,MAAc,gBAAoD,YAAqC;AAC7H,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAA0B;AAC1B,UAAI,cAAc,aAAa,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,UAAU;AAC/D,UAAI,gBAAgB;AAClB,cAAM,WAAW,GAAG,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAC3G,cAAM,WAAW,GAAG,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAC3G,sBAAc,aAAa,QAAQ,KAAK,QAAQ,MAAM;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AACA,aAAS,SAAS,GAAyB,YAAkE;AAC3G,aAAO,EAAE,kBAAkB,GAAG,OAAO,aAAa,EAAE,eAAe,OAAK,eAAe,EAAE,UAAU,KAAK,EAAE,mBAAmB,GAAG,OAAO,aAAa,EAAE,gBAAgB,OAAK,eAAe,EAAE,UAAU;AAAA,IACxM;AACA,aAAS,mBAAmB,GAAsC;AAChE,UAAI,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAAG,QAAO,EAAE,cAAc,CAAC,EAAE;AACrE,UAAI,EAAE,kBAAkB,EAAE,eAAe,CAAC,EAAG,QAAO,EAAE,eAAe,CAAC,EAAE;AAAA,IAC1E;AAOA,aAAS,oBAAoB,KAAqB;AAChD,UAAI,CAAC,IAAK;AACV,UAAI,UAAU,IAAI,eAAe,0BAA0B;AAC3D,UAAI,CAAC,SAAS;AACZ,kBAAU,IAAI,cAAc,OAAO;AACnC,gBAAQ,OAAO;AACf,gBAAQ,KAAK;AACb,gBAAQ,YAAY;AACpB,gBAAQ,aAAa;AACrB,YAAI,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAY,OAAO;AAAA,MACzD;AACA,UAAI,IAAI,KAAM,cAAa,IAAI,MAAM,uCAAuC;AAAA,IAC9E;AACA,aAAS,+BAA+B,KAAqB;AAE3D,UAAI,OAAO,uBAAuB;AAChC,eAAO,sBAAsB,MAAM;AACjC,iCAAuB,GAAG;AAAA,QAC5B,CAAC;AAAA,MACH,OAAO;AACL,+BAAuB,GAAG;AAAA,MAC5B;AAAA,IACF;AACA,aAAS,uBAAuB,KAAqB;AACnD,UAAI,CAAC,IAAK;AACV,UAAI;AACF,YAAI,IAAI,KAAM,iBAAgB,IAAI,MAAM,uCAAuC;AAE/E,YAAI,IAAI,WAAW;AAEjB,cAAI,UAAU,MAAM;AAAA,QACtB,OAAO;AAGL,gBAAM,aAAa,IAAI,eAAe,QAAQ,aAAa;AAC3D,cAAI,aAAa,UAAU,SAAS,SAAS;AAC3C,sBAAU,gBAAgB;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AACA,aAAS,aAAa,IAAsB,WAAwB;AAClE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,IAAI,SAAS;AAAA,MAC5B,OAAO;AACL,YAAI,CAAC,GAAG,UAAU,MAAM,IAAI,OAAO,YAAY,SAAS,SAAS,CAAC,GAAG;AACnE,aAAG,aAAa,IAAI,SAAS;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,aAAS,gBAAgB,IAAsB,WAAwB;AACrE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,OAAO,SAAS;AAAA,MAC/B,OAAO;AACL,WAAG,YAAY,GAAG,UAAU,QAAQ,IAAI,OAAO,YAAY,SAAS,WAAW,GAAG,GAAG,EAAE;AAAA,MACzF;AAAA,IACF;AAAA;AAAA;;;ACtOA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,WAAW;AACnB,YAAQ,WAAW;AACnB,YAAQ,iBAAiB;AACzB,YAAQ,sBAAsB;AAC9B,YAAQ,mBAAmB;AAC3B,YAAQ,qBAAqB;AAC7B,YAAQ,aAAa;AACrB,QAAI,SAAS;AACb,QAAI,UAAU;AAId,aAAS,iBAAiB,WAA2B,GAAgB,GAAsC;AAEzG,UAAI,CAAC,UAAU,MAAM,OAAQ,QAAO,CAAC,GAAG,CAAC;AAGzC,UAAI;AAAA,QACF;AAAA,MACF,IAAI,UAAU;AACd,eAAS,OAAO,WAAW,WAAW,SAAS,YAAY,MAAM;AACjE,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,cAAc,cAAc;AAClC,YAAI;AACJ,YAAI,WAAW,UAAU;AACvB,sBAAY,KAAK;AAAA,QACnB,OAAO;AAKL,gBAAM,WAAa,KAAK,YAAY;AACpC,sBAAY,SAAS,cAAc,MAAM;AAAA,QAC3C;AACA,YAAI,EAAE,qBAAqB,YAAY,cAAc;AACnD,gBAAM,IAAI,MAAM,sBAAsB,SAAS,8BAA8B;AAAA,QAC/E;AACA,cAAM,cAAgC;AACtC,cAAM,YAAY,YAAY,iBAAiB,IAAI;AACnD,cAAM,iBAAiB,YAAY,iBAAiB,WAAW;AAE/D,iBAAS;AAAA,UACP,MAAM,CAAC,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,WAAW,KAAK,GAAG,OAAO,KAAK,UAAU,UAAU;AAAA,UAC3G,KAAK,CAAC,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,UAAU,KAAK,GAAG,OAAO,KAAK,UAAU,SAAS;AAAA,UACvG,QAAQ,GAAG,QAAQ,YAAY,WAAW,KAAK,GAAG,QAAQ,YAAY,IAAI,IAAI,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,YAAY,KAAK,GAAG,OAAO,KAAK,UAAU,WAAW;AAAA,UACpL,SAAS,GAAG,QAAQ,aAAa,WAAW,KAAK,GAAG,QAAQ,aAAa,IAAI,IAAI,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,aAAa,KAAK,GAAG,OAAO,KAAK,UAAU,YAAY;AAAA,QAC1L;AAAA,MACF;AAGA,WAAK,GAAG,OAAO,OAAO,OAAO,KAAK,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,KAAK;AACjE,WAAK,GAAG,OAAO,OAAO,OAAO,MAAM,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,MAAM;AAGnE,WAAK,GAAG,OAAO,OAAO,OAAO,IAAI,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,IAAI;AAC/D,WAAK,GAAG,OAAO,OAAO,OAAO,GAAG,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,GAAG;AAC7D,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,WAAW,MAA6B,UAAuB,UAA6C;AACnH,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AAGA,aAAS,mBAAmB,GAAyB,iBAA+B,eAAyD;AAC3I,YAAM,WAAW,OAAO,oBAAoB,YAAY,GAAG,QAAQ,UAAU,GAAG,eAAe,IAAI;AACnG,UAAI,OAAO,oBAAoB,YAAY,CAAC,SAAU,QAAO;AAC7D,YAAM,OAAO,YAAY,aAAa;AAEtC,YAAM,eAAe,cAAc,MAAM,gBAAgB,KAAK,gBAAgB,KAAK,cAAc;AACjG,cAAQ,GAAG,QAAQ,oBAAoB,YAAY,GAAG,cAAc,cAAc,MAAM,KAAK;AAAA,IAC/F;AAGA,aAAS,eAAe,WAA+B,GAAgB,GAAmC;AACxG,YAAM,UAAU,EAAE,GAAG,OAAO,OAAO,UAAU,KAAK;AAClD,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,SAAS;AAEX,eAAO;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF,OAAO;AAEL,eAAO;AAAA,UACL;AAAA,UACA,QAAQ,IAAI,UAAU;AAAA,UACtB,QAAQ,IAAI,UAAU;AAAA,UACtB,OAAO,UAAU;AAAA,UACjB,OAAO,UAAU;AAAA,UACjB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,aAAS,oBAAoB,WAA2B,UAAiD;AACvG,YAAM,QAAQ,UAAU,MAAM;AAC9B,aAAO;AAAA,QACL,MAAM,SAAS;AAAA,QACf,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,QAAQ,SAAS,SAAS;AAAA,QAC1B,QAAQ,SAAS,SAAS;AAAA,QAC1B,OAAO,UAAU,MAAM;AAAA,QACvB,OAAO,UAAU,MAAM;AAAA,MACzB;AAAA,IACF;AAGA,aAAS,YAAY,QAAiC;AACpD,aAAO;AAAA,QACL,MAAM,OAAO;AAAA,QACb,KAAK,OAAO;AAAA,QACZ,OAAO,OAAO;AAAA,QACd,QAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,aAAS,YAAY,WAA4D;AAC/E,YAAM,OAAO,UAAU,YAAY;AACnC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpJA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,aAAS,MAAM;AACb,UAAI,OAAW,SAAQ,IAAI,GAAG,SAAS;AAAA,IACzC;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AAIvT,QAAM,YAAY;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAGA,QAAI,eAAe,UAAU;AAqC7B,QAAM,gBAAN,cAA4B,MAAM,UAAqC;AAAA,MACrE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,wBAAgB,MAAM,YAAY,KAAK;AAEvC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,mBAAmB,IAAI;AAC7C,wBAAgB,MAAM,WAAW,KAAK;AACtC,wBAAgB,MAAM,mBAAmB,OAAK;AAE5C,eAAK,MAAM,YAAY,CAAC;AAGxB,cAAI,CAAC,KAAK,MAAM,iBAAiB,OAAO,EAAE,WAAW,YAAY,EAAE,WAAW,EAAG,QAAO;AAGxF,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC,SAAS,cAAc,MAAM;AACxE,kBAAM,IAAI,MAAM,2CAA2C;AAAA,UAC7D;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AAGJ,cAAI,KAAK,MAAM,YAAY,EAAE,EAAE,kBAAkB,cAAc,YAAY,SAAS,KAAK,MAAM,UAAU,EAAE,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,GAAG;AACjS;AAAA,UACF;AAIA,cAAI,EAAE,SAAS,gBAAgB,CAAC,KAAK,MAAM,kBAAmB,GAAE,eAAe;AAK/E,gBAAM,mBAAmB,GAAG,QAAQ,oBAAoB,CAAC;AACzD,eAAK,kBAAkB;AAGvB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,iBAAiB,IAAI;AAC9E,cAAI,YAAY,KAAM;AACtB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,sCAAsC,SAAS;AAGjE,WAAC,GAAG,KAAK,SAAS,WAAW,KAAK,MAAM,OAAO;AAC/C,gBAAM,eAAe,KAAK,MAAM,QAAQ,GAAG,SAAS;AACpD,cAAI,iBAAiB,SAAS,KAAK,YAAY,MAAO;AAItD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,qBAAqB,aAAa;AAKnF,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AAKb,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,UAAU;AACvE,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,QAC7E,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,OACpB,SAAS,IAAI,KAAK;AACpB,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,CAAC,UAAU,CAAC,OAAQ;AACxB,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,iCAAiC,SAAS;AAG5D,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,SAAS;AACnD,cAAI,iBAAiB,SAAS,KAAK,YAAY,OAAO;AACpD,gBAAI;AAEF,mBAAK,eAAe,IAAI,WAAW,SAAS,CAAC;AAAA,YAC/C,SAAS,KAAK;AAEZ,oBAAM,QAAU,SAAS,YAAY,aAAa;AAGlD,oBAAM,eAAe,WAAW,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AACtG,mBAAK,eAAe,KAAK;AAAA,YAC3B;AACA;AAAA,UACF;AACA,eAAK,QAAQ;AACb,eAAK,QAAQ;AAAA,QACf,CAAC;AACD,wBAAgB,MAAM,kBAAkB,OAAK;AAC3C,cAAI,CAAC,KAAK,SAAU;AACpB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAG7D,gBAAM,iBAAiB,KAAK,MAAM,OAAO,GAAG,SAAS;AACrD,cAAI,mBAAmB,SAAS,KAAK,YAAY,MAAO,QAAO;AAC/D,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,UAAU;AAEZ,gBAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,gCAAgC,SAAS,aAAa;AAAA,UACzG;AACA,WAAC,GAAG,KAAK,SAAS,qCAAqC,SAAS;AAGhE,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AACb,cAAI,UAAU;AAEZ,aAAC,GAAG,KAAK,SAAS,kCAAkC;AACpD,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,UAAU;AACnF,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,UACzF;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,eAAe,OAAK;AACxC,yBAAe,UAAU;AAEzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,aAAa,OAAK;AACtC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAED,wBAAgB,MAAM,gBAAgB,OAAK;AAEzC,yBAAe,UAAU;AACzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,WAAC,GAAG,QAAQ,UAAU,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YACxE,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YAC3E,SAAS;AAAA,UACX,CAAC;AACD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,gCAAgC,aAAa;AAAA,QAChG;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAgC;AAC9B,eAAO,KAAK,OAAO,UAAU,KAAK,OAAO,SAAS,UAAU,UAAU,QAAQ,YAAY,IAAI;AAAA,MAChG;AAAA,MACA,SAAiC;AAG/B,eAAoB,MAAM,aAAa,MAAM,SAAS,KAAK,KAAK,MAAM,QAAQ,GAAG;AAAA;AAAA;AAAA,UAG/E,aAAa,KAAK;AAAA,UAClB,WAAW,KAAK;AAAA;AAAA;AAAA;AAAA,UAIhB,YAAY,KAAK;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,eAAe,eAAe,eAAe;AAC7D,oBAAgB,eAAe,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO1C,eAAe,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQlC,mBAAmB,WAAW,QAAQ;AAAA,MACtC,UAAU,WAAW,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAKlC,UAAU,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM7B,sBAAsB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKzC,cAAc,SAAU,OAAgC,UAA0C;AAChG,YAAI,MAAM,QAAQ,KAAK,MAAM,QAAQ,EAAE,aAAa,GAAG;AACrD,gBAAM,IAAI,MAAM,8CAA+C;AAAA,QACjE;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA,MAAM,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB1D,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB3B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,aAAa,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIhC,OAAO,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI1B,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgB,eAAe,gBAAgB;AAAA,MAC7C,eAAe;AAAA;AAAA,MAEf,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,SAAS,WAAY;AAAA,MAAC;AAAA,MACtB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,aAAa,WAAY;AAAA,MAAC;AAAA,MAC1B,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;ACzbD;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,eAAO,eAAe;AAAA,MACxB;AAAA,IACF,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,iBAAiB,uBAAuB,uBAA0B;AACtE,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,wBAAwB,GAAG,GAAG;AAAE,UAAI,cAAc,OAAO,QAAS,KAAI,IAAI,oBAAI,QAAQ,GAAG,IAAI,oBAAI,QAAQ;AAAG,cAAQ,0BAA0B,SAAUC,IAAGC,IAAG;AAAE,YAAI,CAACA,MAAKD,MAAKA,GAAE,WAAY,QAAOA;AAAG,YAAI,GAAG,GAAG,IAAI,EAAE,WAAW,MAAM,SAASA,GAAE;AAAG,YAAI,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,GAAG,QAAO;AAAG,YAAI,IAAIC,KAAI,IAAI,GAAG;AAAE,cAAI,EAAE,IAAID,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAAG,YAAE,IAAIA,IAAG,CAAC;AAAA,QAAG;AAAE,mBAAWC,MAAKD,GAAG,eAAcC,MAAK,CAAC,EAAE,eAAe,KAAKD,IAAGC,EAAC,OAAO,KAAK,IAAI,OAAO,mBAAmB,OAAO,yBAAyBD,IAAGC,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGA,IAAG,CAAC,IAAI,EAAEA,EAAC,IAAID,GAAEC,EAAC;AAAI,eAAO;AAAA,MAAG,GAAG,GAAG,CAAC;AAAA,IAAG;AACrmB,aAAS,WAAW;AAAE,aAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,IAAI,UAAU,CAAC;AAAG,mBAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAAI;AAAE,eAAO;AAAA,MAAG,GAAG,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AACnR,aAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,cAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,IAAG;AACnL,aAAS,eAAe,GAAG;AAAE,UAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,aAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAA,IAAI;AAC1G,aAAS,aAAa,GAAG,GAAG;AAAE,UAAI,YAAY,OAAO,KAAK,CAAC,EAAG,QAAO;AAAG,UAAI,IAAI,EAAE,OAAO,WAAW;AAAG,UAAI,WAAW,GAAG;AAAE,YAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,YAAI,YAAY,OAAO,EAAG,QAAO;AAAG,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAA,IAAG;AA8BvT,QAAM,YAAN,cAAwB,MAAM,UAAiD;AAAA;AAAA;AAAA,MAG7E,OAAO,yBAAyB,MAAc,OAA6C;AACzF,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AACzB,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AAEzB,YAAI,aAAa,CAAC,qBAAqB,SAAS,MAAM,kBAAkB,KAAK,SAAS,MAAM,kBAAkB,IAAI;AAChH,WAAC,GAAG,KAAK,SAAS,0CAA0C;AAAA,YAC1D;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,YACL,GAAG,SAAS;AAAA,YACZ,GAAG,SAAS;AAAA,YACZ,mBAAmB;AAAA,cACjB,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,YAAY,OAA4B;AACtC,cAAM,KAAK;AACX,wBAAgB,MAAM,eAAe,CAAC,GAAG,aAAa;AACpD,WAAC,GAAG,KAAK,SAAS,8BAA8B,QAAQ;AAGxD,gBAAM,cAAc,KAAK,MAAM,QAAQ,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AAE/F,cAAI,gBAAgB,MAAO,QAAO;AAClC,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,UACX,CAAC;AAAA,QACH,CAAC;AACD,wBAAgB,MAAM,UAAU,CAAC,GAAG,aAAa;AAC/C,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AACjC,WAAC,GAAG,KAAK,SAAS,yBAAyB,QAAQ;AACnD,gBAAM,UAAU,GAAG,aAAa,qBAAqB,MAAM,QAAQ;AACnE,gBAAM,WAAW;AAAA,YACf,GAAG,OAAO;AAAA,YACV,GAAG,OAAO;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAGA,cAAI,KAAK,MAAM,QAAQ;AAErB,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI;AAKJ,qBAAS,KAAK,KAAK,MAAM;AACzB,qBAAS,KAAK,KAAK,MAAM;AAGzB,kBAAM,CAAC,WAAW,SAAS,KAAK,GAAG,aAAa,kBAAkB,MAAM,SAAS,GAAG,SAAS,CAAC;AAC9F,qBAAS,IAAI;AACb,qBAAS,IAAI;AAGb,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AACpD,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AAGpD,mBAAO,IAAI,SAAS;AACpB,mBAAO,IAAI,SAAS;AACpB,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AACxC,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AAAA,UAC1C;AAGA,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,MAAM;AAChD,cAAI,iBAAiB,MAAO,QAAO;AACnC,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,wBAAgB,MAAM,cAAc,CAAC,GAAG,aAAa;AACnD,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AAGjC,gBAAM,iBAAiB,KAAK,MAAM,OAAO,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AACjG,cAAI,mBAAmB,MAAO,QAAO;AACrC,WAAC,GAAG,KAAK,SAAS,6BAA6B,QAAQ;AACvD,gBAAM,WAAyC;AAAA,YAC7C,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAIA,gBAAM,aAAa,QAAQ,KAAK,MAAM,QAAQ;AAC9C,cAAI,YAAY;AACd,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,KAAK,MAAM;AACf,qBAAS,IAAI;AACb,qBAAS,IAAI;AAAA,UACf;AACA,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,aAAK,QAAQ;AAAA;AAAA,UAEX,UAAU;AAAA;AAAA,UAEV,SAAS;AAAA;AAAA,UAET,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,mBAAmB;AAAA,YACjB,GAAG,MAAM;AAAA,UACX;AAAA;AAAA,UAEA,QAAQ;AAAA,UACR,QAAQ;AAAA;AAAA,UAER,cAAc;AAAA,QAChB;AACA,YAAI,MAAM,YAAY,EAAE,MAAM,UAAU,MAAM,SAAS;AAErD,kBAAQ,KAAK,2NAAqO;AAAA,QACpP;AAAA,MACF;AAAA,MACA,oBAAoB;AAElB,YAAI,OAAO,OAAO,eAAe,eAAe,KAAK,YAAY,aAAa,OAAO,YAAY;AAC/F,eAAK,SAAS;AAAA,YACZ,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,YAAI,KAAK,MAAM,UAAU;AACvB,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAgC;AAC9B,eAAO,KAAK,OAAO,SAAS,WAAW,UAAU,QAAQ,YAAY,IAAI;AAAA,MAC3E;AAAA,MACA,SAAgC;AAC9B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL,IAAI,KAAK;AACT,YAAI,QAAQ,CAAC;AACb,YAAI,eAAe;AAGnB,cAAM,aAAa,QAAQ,QAAQ;AACnC,cAAM,YAAY,CAAC,cAAc,KAAK,MAAM;AAC5C,cAAM,gBAAgB,YAAY;AAClC,cAAM,gBAAgB;AAAA;AAAA,UAEpB,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA;AAAA,UAEhF,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA,QAClF;AAGA,YAAI,KAAK,MAAM,cAAc;AAC3B,0BAAgB,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QAC9E,OAAO;AAKL,mBAAS,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QACvE;AAGA,cAAM,aAAa,GAAG,MAAM,MAAM,SAAS,MAAM,aAAa,IAAI,kBAAkB;AAAA,UAClF,CAAC,wBAAwB,GAAG,KAAK,MAAM;AAAA,UACvC,CAAC,uBAAuB,GAAG,KAAK,MAAM;AAAA,QACxC,CAAC;AAID,eAAoB,MAAM,cAAc,eAAe,SAAS,SAAS,CAAC,GAAG,oBAAoB;AAAA,UAC/F,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,QACf,CAAC,GAAgB,MAAM,aAAa,MAAM,SAAS,KAAK,QAAQ,GAAG;AAAA,UACjE;AAAA,UACA,OAAO;AAAA,YACL,GAAG,SAAS,MAAM;AAAA,YAClB,GAAG;AAAA,UACL;AAAA,UACA,WAAW;AAAA,QACb,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,WAAW,eAAe,WAAW;AACrD,oBAAgB,WAAW,aAAa;AAAA;AAAA,MAEtC,GAAG,eAAe,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAc1B,MAAM,WAAW,QAAQ,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2BzD,QAAQ,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,MAAM;AAAA,QAC7D,MAAM,WAAW,QAAQ;AAAA,QACzB,OAAO,WAAW,QAAQ;AAAA,QAC1B,KAAK,WAAW,QAAQ;AAAA,QACxB,QAAQ,WAAW,QAAQ;AAAA,MAC7B,CAAC,GAAG,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,MACjE,kBAAkB,WAAW,QAAQ;AAAA,MACrC,0BAA0B,WAAW,QAAQ;AAAA,MAC7C,yBAAyB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB5C,iBAAiB,WAAW,QAAQ,MAAM;AAAA,QACxC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA,MACD,gBAAgB,WAAW,QAAQ,MAAM;AAAA,QACvC,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,QACtF,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,MACxF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBD,UAAU,WAAW,QAAQ,MAAM;AAAA,QACjC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA,MAID,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgB,WAAW,gBAAgB;AAAA,MACzC,GAAG,eAAe,QAAQ;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,0BAA0B;AAAA,MAC1B,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;AC1YD;AAAA;AAEA,QAAM;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,IACF,IAAI;AAKJ,WAAO,UAAU;AACjB,WAAO,QAAQ,UAAU;AACzB,WAAO,QAAQ,gBAAgB;AAAA;AAAA;", "names": ["i", "checker", "e", "e", "t", "e", "t", "e", "t"]}