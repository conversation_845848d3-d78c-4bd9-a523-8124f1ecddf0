import React, { useState, useEffect, useCallback, useRef } from 'react';

const BOARD_WIDTH = 10;
const BOARD_HEIGHT = 20;
const BLOCK_SIZE = 20;

const TETROMINOES = {
  I: { shape: [[1, 1, 1, 1]], color: '#00ffff' },
  O: { shape: [[1, 1], [1, 1]], color: '#ffff00' },
  T: { shape: [[0, 1, 0], [1, 1, 1]], color: '#800080' },
  S: { shape: [[0, 1, 1], [1, 1, 0]], color: '#00ff00' },
  Z: { shape: [[1, 1, 0], [0, 1, 1]], color: '#ff0000' },
  J: { shape: [[1, 0, 0], [1, 1, 1]], color: '#0000ff' },
  L: { shape: [[0, 0, 1], [1, 1, 1]], color: '#ffa500' }
};

interface Position {
  x: number;
  y: number;
}

interface Piece {
  shape: number[][];
  color: string;
  position: Position;
}

const Tetris: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [board, setBoard] = useState<string[][]>(() => 
    Array(BOARD_HEIGHT).fill(null).map(() => Array(BOARD_WIDTH).fill(''))
  );
  const [currentPiece, setCurrentPiece] = useState<Piece | null>(null);
  const [score, setScore] = useState(0);
  const [lines, setLines] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);

  const createRandomPiece = useCallback((): Piece => {
    const pieces = Object.keys(TETROMINOES);
    const randomPiece = pieces[Math.floor(Math.random() * pieces.length)] as keyof typeof TETROMINOES;
    const tetromino = TETROMINOES[randomPiece];
    
    return {
      shape: tetromino.shape,
      color: tetromino.color,
      position: { x: Math.floor(BOARD_WIDTH / 2) - 1, y: 0 }
    };
  }, []);

  const isValidMove = useCallback((piece: Piece, newPosition: Position): boolean => {
    for (let y = 0; y < piece.shape.length; y++) {
      for (let x = 0; x < piece.shape[y].length; x++) {
        if (piece.shape[y][x]) {
          const newX = newPosition.x + x;
          const newY = newPosition.y + y;
          
          if (newX < 0 || newX >= BOARD_WIDTH || newY >= BOARD_HEIGHT) {
            return false;
          }
          
          if (newY >= 0 && board[newY][newX]) {
            return false;
          }
        }
      }
    }
    return true;
  }, [board]);

  const rotatePiece = useCallback((piece: Piece): number[][] => {
    const rotated = piece.shape[0].map((_, index) =>
      piece.shape.map(row => row[index]).reverse()
    );
    return rotated;
  }, []);

  const placePiece = useCallback((piece: Piece) => {
    const newBoard = board.map(row => [...row]);
    
    for (let y = 0; y < piece.shape.length; y++) {
      for (let x = 0; x < piece.shape[y].length; x++) {
        if (piece.shape[y][x]) {
          const boardY = piece.position.y + y;
          const boardX = piece.position.x + x;
          if (boardY >= 0) {
            newBoard[boardY][boardX] = piece.color;
          }
        }
      }
    }
    
    setBoard(newBoard);
  }, [board]);

  const clearLines = useCallback(() => {
    const newBoard = board.filter(row => row.some(cell => !cell));
    const clearedLines = BOARD_HEIGHT - newBoard.length;
    
    if (clearedLines > 0) {
      const emptyRows = Array(clearedLines).fill(null).map(() => Array(BOARD_WIDTH).fill(''));
      setBoard([...emptyRows, ...newBoard]);
      setLines(prev => prev + clearedLines);
      setScore(prev => prev + clearedLines * 100 * (clearedLines === 4 ? 2 : 1)); // Tetris bonus
    }
  }, [board]);

  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (!currentPiece || gameOver || !gameStarted) return;

    switch (e.key) {
      case 'ArrowLeft':
        const leftPos = { ...currentPiece.position, x: currentPiece.position.x - 1 };
        if (isValidMove(currentPiece, leftPos)) {
          setCurrentPiece({ ...currentPiece, position: leftPos });
        }
        break;
      case 'ArrowRight':
        const rightPos = { ...currentPiece.position, x: currentPiece.position.x + 1 };
        if (isValidMove(currentPiece, rightPos)) {
          setCurrentPiece({ ...currentPiece, position: rightPos });
        }
        break;
      case 'ArrowDown':
        const downPos = { ...currentPiece.position, y: currentPiece.position.y + 1 };
        if (isValidMove(currentPiece, downPos)) {
          setCurrentPiece({ ...currentPiece, position: downPos });
        }
        break;
      case 'ArrowUp':
        const rotatedShape = rotatePiece(currentPiece);
        const rotatedPiece = { ...currentPiece, shape: rotatedShape };
        if (isValidMove(rotatedPiece, currentPiece.position)) {
          setCurrentPiece(rotatedPiece);
        }
        break;
    }
  }, [currentPiece, gameOver, gameStarted, isValidMove, rotatePiece]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  useEffect(() => {
    if (!gameStarted || gameOver) return;

    const gameLoop = setInterval(() => {
      if (currentPiece) {
        const newPosition = { ...currentPiece.position, y: currentPiece.position.y + 1 };
        
        if (isValidMove(currentPiece, newPosition)) {
          setCurrentPiece({ ...currentPiece, position: newPosition });
        } else {
          placePiece(currentPiece);
          clearLines();
          
          const newPiece = createRandomPiece();
          if (!isValidMove(newPiece, newPiece.position)) {
            setGameOver(true);
          } else {
            setCurrentPiece(newPiece);
          }
        }
      }
    }, 500);

    return () => clearInterval(gameLoop);
  }, [currentPiece, gameStarted, gameOver, isValidMove, placePiece, clearLines, createRandomPiece]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, BOARD_WIDTH * BLOCK_SIZE, BOARD_HEIGHT * BLOCK_SIZE);

    // Draw board
    for (let y = 0; y < BOARD_HEIGHT; y++) {
      for (let x = 0; x < BOARD_WIDTH; x++) {
        if (board[y][x]) {
          ctx.fillStyle = board[y][x];
          ctx.fillRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE - 1, BLOCK_SIZE - 1);
        }
      }
    }

    // Draw current piece
    if (currentPiece) {
      ctx.fillStyle = currentPiece.color;
      for (let y = 0; y < currentPiece.shape.length; y++) {
        for (let x = 0; x < currentPiece.shape[y].length; x++) {
          if (currentPiece.shape[y][x]) {
            const drawX = (currentPiece.position.x + x) * BLOCK_SIZE;
            const drawY = (currentPiece.position.y + y) * BLOCK_SIZE;
            ctx.fillRect(drawX, drawY, BLOCK_SIZE - 1, BLOCK_SIZE - 1);
          }
        }
      }
    }
  }, [board, currentPiece]);

  const startGame = () => {
    setGameStarted(true);
    setCurrentPiece(createRandomPiece());
  };

  const resetGame = () => {
    setBoard(Array(BOARD_HEIGHT).fill(null).map(() => Array(BOARD_WIDTH).fill('')));
    setCurrentPiece(null);
    setScore(0);
    setLines(0);
    setGameOver(false);
    setGameStarted(false);
  };

  return (
    <div style={{ textAlign: 'center', padding: '10px' }}>
      <h3 style={{ margin: '0 0 10px 0', color: '#000080' }}>🧩 Tetris</h3>
      
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', fontSize: '11px' }}>
        <div><strong>Score: {score}</strong></div>
        <div><strong>Lines: {lines}</strong></div>
      </div>

      <canvas
        ref={canvasRef}
        width={BOARD_WIDTH * BLOCK_SIZE}
        height={BOARD_HEIGHT * BLOCK_SIZE}
        style={{
          border: '2px solid #808080',
          background: '#000',
          display: 'block',
          margin: '0 auto 10px'
        }}
      />

      {!gameStarted && !gameOver && (
        <div>
          <button onClick={startGame} style={{
            background: '#c0c0c0', border: '2px outset #c0c0c0', padding: '8px 16px',
            cursor: 'pointer', fontSize: '11px', fontWeight: 'bold'
          }}>
            Start Game
          </button>
          <div style={{ fontSize: '10px', marginTop: '8px' }}>
            Arrow keys: Move/Rotate • Down: Drop faster
          </div>
        </div>
      )}

      {gameOver && (
        <div>
          <div style={{ color: '#ff0000', fontWeight: 'bold', marginBottom: '8px' }}>
            Game Over! Score: {score}
          </div>
          <button onClick={resetGame} style={{
            background: '#c0c0c0', border: '2px outset #c0c0c0', padding: '8px 16px',
            cursor: 'pointer', fontSize: '11px', fontWeight: 'bold'
          }}>
            Play Again
          </button>
        </div>
      )}
    </div>
  );
};

export default Tetris;
