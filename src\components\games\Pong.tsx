import React, { useState, useEffect, useCallback, useRef } from 'react';

const CANVAS_WIDTH = 400;
const CANVAS_HEIGHT = 300;
const PADDLE_HEIGHT = 60;
const PADDLE_WIDTH = 10;
const BALL_SIZE = 8;

interface Position {
  x: number;
  y: number;
}

interface Ball {
  position: Position;
  velocity: Position;
}

const Pong: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [leftPaddle, setLeftPaddle] = useState(CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2);
  const [rightPaddle, setRightPaddle] = useState(CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2);
  const [ball, setBall] = useState<Ball>({
    position: { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT / 2 },
    velocity: { x: 3, y: 2 }
  });
  const [leftScore, setLeftScore] = useState(0);
  const [rightScore, setRightScore] = useState(0);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [keys, setKeys] = useState<Set<string>>(new Set());

  const resetBall = useCallback(() => {
    setBall({
      position: { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT / 2 },
      velocity: { 
        x: Math.random() > 0.5 ? 3 : -3, 
        y: (Math.random() - 0.5) * 4 
      }
    });
  }, []);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    setKeys(prev => new Set(prev).add(e.key));
  }, []);

  const handleKeyUp = useCallback((e: KeyboardEvent) => {
    setKeys(prev => {
      const newKeys = new Set(prev);
      newKeys.delete(e.key);
      return newKeys;
    });
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp]);

  useEffect(() => {
    if (!gameStarted || gameOver) return;

    const gameLoop = setInterval(() => {
      // Move paddles
      setLeftPaddle(prev => {
        let newPos = prev;
        if (keys.has('w') || keys.has('W')) newPos -= 5;
        if (keys.has('s') || keys.has('S')) newPos += 5;
        return Math.max(0, Math.min(CANVAS_HEIGHT - PADDLE_HEIGHT, newPos));
      });

      setRightPaddle(prev => {
        let newPos = prev;
        if (keys.has('ArrowUp')) newPos -= 5;
        if (keys.has('ArrowDown')) newPos += 5;
        return Math.max(0, Math.min(CANVAS_HEIGHT - PADDLE_HEIGHT, newPos));
      });

      // Move ball
      setBall(prevBall => {
        let newBall = {
          position: {
            x: prevBall.position.x + prevBall.velocity.x,
            y: prevBall.position.y + prevBall.velocity.y
          },
          velocity: { ...prevBall.velocity }
        };

        // Ball collision with top/bottom walls
        if (newBall.position.y <= 0 || newBall.position.y >= CANVAS_HEIGHT - BALL_SIZE) {
          newBall.velocity.y = -newBall.velocity.y;
        }

        // Ball collision with left paddle
        if (newBall.position.x <= PADDLE_WIDTH &&
            newBall.position.y >= leftPaddle &&
            newBall.position.y <= leftPaddle + PADDLE_HEIGHT) {
          newBall.velocity.x = Math.abs(newBall.velocity.x);
          // Add some spin based on where it hits the paddle
          const hitPos = (newBall.position.y - leftPaddle) / PADDLE_HEIGHT;
          newBall.velocity.y = (hitPos - 0.5) * 6;
        }

        // Ball collision with right paddle
        if (newBall.position.x >= CANVAS_WIDTH - PADDLE_WIDTH - BALL_SIZE &&
            newBall.position.y >= rightPaddle &&
            newBall.position.y <= rightPaddle + PADDLE_HEIGHT) {
          newBall.velocity.x = -Math.abs(newBall.velocity.x);
          // Add some spin based on where it hits the paddle
          const hitPos = (newBall.position.y - rightPaddle) / PADDLE_HEIGHT;
          newBall.velocity.y = (hitPos - 0.5) * 6;
        }

        // Score points
        if (newBall.position.x < 0) {
          setRightScore(prev => prev + 1);
          setTimeout(resetBall, 1000);
          return prevBall;
        }
        if (newBall.position.x > CANVAS_WIDTH) {
          setLeftScore(prev => prev + 1);
          setTimeout(resetBall, 1000);
          return prevBall;
        }

        return newBall;
      });
    }, 16); // ~60 FPS

    return () => clearInterval(gameLoop);
  }, [gameStarted, gameOver, keys, leftPaddle, rightPaddle, resetBall]);

  useEffect(() => {
    if (leftScore >= 5 || rightScore >= 5) {
      setGameOver(true);
    }
  }, [leftScore, rightScore]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // Draw center line
    ctx.strokeStyle = '#fff';
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(CANVAS_WIDTH / 2, 0);
    ctx.lineTo(CANVAS_WIDTH / 2, CANVAS_HEIGHT);
    ctx.stroke();
    ctx.setLineDash([]);

    // Draw paddles
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, leftPaddle, PADDLE_WIDTH, PADDLE_HEIGHT);
    ctx.fillRect(CANVAS_WIDTH - PADDLE_WIDTH, rightPaddle, PADDLE_WIDTH, PADDLE_HEIGHT);

    // Draw ball
    ctx.fillRect(ball.position.x, ball.position.y, BALL_SIZE, BALL_SIZE);
  }, [leftPaddle, rightPaddle, ball]);

  const startGame = () => {
    setGameStarted(true);
    resetBall();
  };

  const resetGame = () => {
    setLeftScore(0);
    setRightScore(0);
    setGameStarted(false);
    setGameOver(false);
    setLeftPaddle(CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2);
    setRightPaddle(CANVAS_HEIGHT / 2 - PADDLE_HEIGHT / 2);
    resetBall();
  };

  return (
    <div style={{ textAlign: 'center', padding: '10px' }}>
      <h3 style={{ margin: '0 0 10px 0', color: '#000080' }}>🏓 Pong</h3>
      
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', fontSize: '12px' }}>
        <div><strong>Player 1: {leftScore}</strong></div>
        <div><strong>Player 2: {rightScore}</strong></div>
      </div>

      <canvas
        ref={canvasRef}
        width={CANVAS_WIDTH}
        height={CANVAS_HEIGHT}
        style={{
          border: '2px solid #808080',
          background: '#000',
          display: 'block',
          margin: '0 auto 10px'
        }}
      />

      {!gameStarted && !gameOver && (
        <div>
          <button onClick={startGame} style={{
            background: '#c0c0c0', border: '2px outset #c0c0c0', padding: '8px 16px',
            cursor: 'pointer', fontSize: '11px', fontWeight: 'bold'
          }}>
            Start Game
          </button>
          <div style={{ fontSize: '10px', marginTop: '8px' }}>
            Player 1: W/S keys • Player 2: Arrow Up/Down
          </div>
        </div>
      )}

      {gameOver && (
        <div>
          <div style={{ color: '#ff0000', fontWeight: 'bold', marginBottom: '8px' }}>
            Game Over! {leftScore > rightScore ? 'Player 1' : 'Player 2'} Wins!
          </div>
          <button onClick={resetGame} style={{
            background: '#c0c0c0', border: '2px outset #c0c0c0', padding: '8px 16px',
            cursor: 'pointer', fontSize: '11px', fontWeight: 'bold'
          }}>
            Play Again
          </button>
        </div>
      )}

      {gameStarted && !gameOver && (
        <div style={{ fontSize: '10px' }}>
          First to 5 points wins! • Player 1: W/S • Player 2: ↑/↓
        </div>
      )}
    </div>
  );
};

export default Pong;
