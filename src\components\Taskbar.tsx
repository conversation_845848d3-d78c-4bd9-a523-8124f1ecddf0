import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WindowData } from './Desktop';
import './Taskbar.css';

interface TaskbarProps {
  windows: WindowData[];
  onWindowClick: (id: string) => void;
}

const Taskbar: React.FC<TaskbarProps> = ({ windows, onWindowClick }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showStartMenu, setShowStartMenu] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleStartClick = () => {
    setShowStartMenu(!showStartMenu);
  };

  const handleStartMenuClose = () => {
    setShowStartMenu(false);
  };

  const startMenuItems = [
    { icon: '📁', label: 'Programs', hasSubmenu: true },
    { icon: '📄', label: 'Documents', hasSubmenu: true },
    { icon: '⚙️', label: 'Settings', hasSubmenu: true },
    { icon: '🔍', label: 'Find', hasSubmenu: true },
    { icon: '❓', label: 'Help' },
    { icon: '🏃', label: 'Run...' },
    { separator: true },
    { icon: '🔌', label: 'Shut Down...' }
  ];

  return (
    <>
      {showStartMenu && (
        <div className="start-menu-overlay" onClick={handleStartMenuClose} />
      )}
      
      <div className="taskbar">
        <div className="taskbar-left">
          <button 
            className={`start-button ${showStartMenu ? 'active' : ''}`}
            onClick={handleStartClick}
          >
            <span className="start-icon">🪟</span>
            <span className="start-text">Start</span>
          </button>
          
          <div className="taskbar-separator" />
          
          <div className="taskbar-windows">
            {windows.map(window => (
              <motion.button
                key={window.id}
                className={`taskbar-window ${window.isMinimized ? 'minimized' : 'active'}`}
                onClick={() => onWindowClick(window.id)}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                <span className="taskbar-window-icon">📄</span>
                <span className="taskbar-window-title">{window.title}</span>
              </motion.button>
            ))}
          </div>
        </div>
        
        <div className="taskbar-right">
          <div className="system-tray">
            <div className="tray-icon" title="Volume">🔊</div>
            <div className="tray-icon" title="Network">🌐</div>
          </div>
          
          <div className="taskbar-separator" />
          
          <div className="taskbar-clock" title={currentTime.toLocaleDateString()}>
            {formatTime(currentTime)}
          </div>
        </div>
      </div>

      <AnimatePresence>
        {showStartMenu && (
          <motion.div
            className="start-menu"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.15 }}
          >
            <div className="start-menu-header">
              <div className="start-menu-banner">
                <span className="banner-text">Windows 95</span>
              </div>
            </div>
            
            <div className="start-menu-items">
              {startMenuItems.map((item, index) => (
                item.separator ? (
                  <div key={index} className="start-menu-separator" />
                ) : (
                  <div 
                    key={index} 
                    className="start-menu-item"
                    onClick={handleStartMenuClose}
                  >
                    <span className="menu-item-icon">{item.icon}</span>
                    <span className="menu-item-label">{item.label}</span>
                    {item.hasSubmenu && <span className="menu-item-arrow">▶</span>}
                  </div>
                )
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Taskbar;
