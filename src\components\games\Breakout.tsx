import React, { useState, useEffect, useCallback, useRef } from 'react';

const CANVAS_WIDTH = 400;
const CANVAS_HEIGHT = 300;
const PADDLE_WIDTH = 60;
const PADDLE_HEIGHT = 10;
const BALL_SIZE = 8;
const BRICK_WIDTH = 40;
const BRICK_HEIGHT = 15;
const BRICK_ROWS = 5;
const BRICK_COLS = 10;

interface Position {
  x: number;
  y: number;
}

interface Ball {
  position: Position;
  velocity: Position;
}

interface Brick {
  x: number;
  y: number;
  visible: boolean;
  color: string;
}

const Breakout: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [paddle, setPaddle] = useState(CANVAS_WIDTH / 2 - PADDLE_WIDTH / 2);
  const [ball, setBall] = useState<Ball>({
    position: { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - 50 },
    velocity: { x: 3, y: -3 }
  });
  const [bricks, setBricks] = useState<Brick[]>([]);
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameOver, setGameOver] = useState(false);
  const [gameWon, setGameWon] = useState(false);
  const [keys, setKeys] = useState<Set<string>>(new Set());

  const initializeBricks = useCallback(() => {
    const newBricks: Brick[] = [];
    const colors = ['#ff0000', '#ff8000', '#ffff00', '#00ff00', '#0000ff'];
    
    for (let row = 0; row < BRICK_ROWS; row++) {
      for (let col = 0; col < BRICK_COLS; col++) {
        newBricks.push({
          x: col * BRICK_WIDTH,
          y: row * BRICK_HEIGHT + 30,
          visible: true,
          color: colors[row]
        });
      }
    }
    setBricks(newBricks);
  }, []);

  const resetBall = useCallback(() => {
    setBall({
      position: { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - 50 },
      velocity: { x: Math.random() > 0.5 ? 3 : -3, y: -3 }
    });
  }, []);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    setKeys(prev => new Set(prev).add(e.key));
  }, []);

  const handleKeyUp = useCallback((e: KeyboardEvent) => {
    setKeys(prev => {
      const newKeys = new Set(prev);
      newKeys.delete(e.key);
      return newKeys;
    });
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp]);

  useEffect(() => {
    initializeBricks();
  }, [initializeBricks]);

  useEffect(() => {
    if (!gameStarted || gameOver || gameWon) return;

    const gameLoop = setInterval(() => {
      // Move paddle
      setPaddle(prev => {
        let newPos = prev;
        if (keys.has('ArrowLeft') || keys.has('a') || keys.has('A')) newPos -= 6;
        if (keys.has('ArrowRight') || keys.has('d') || keys.has('D')) newPos += 6;
        return Math.max(0, Math.min(CANVAS_WIDTH - PADDLE_WIDTH, newPos));
      });

      // Move ball
      setBall(prevBall => {
        let newBall = {
          position: {
            x: prevBall.position.x + prevBall.velocity.x,
            y: prevBall.position.y + prevBall.velocity.y
          },
          velocity: { ...prevBall.velocity }
        };

        // Ball collision with walls
        if (newBall.position.x <= 0 || newBall.position.x >= CANVAS_WIDTH - BALL_SIZE) {
          newBall.velocity.x = -newBall.velocity.x;
        }
        if (newBall.position.y <= 0) {
          newBall.velocity.y = -newBall.velocity.y;
        }

        // Ball collision with paddle
        if (newBall.position.y >= CANVAS_HEIGHT - PADDLE_HEIGHT - BALL_SIZE &&
            newBall.position.x >= paddle &&
            newBall.position.x <= paddle + PADDLE_WIDTH) {
          newBall.velocity.y = -Math.abs(newBall.velocity.y);
          // Add angle based on where it hits the paddle
          const hitPos = (newBall.position.x - paddle) / PADDLE_WIDTH;
          newBall.velocity.x = (hitPos - 0.5) * 6;
        }

        // Ball collision with bricks
        setBricks(prevBricks => {
          const newBricks = [...prevBricks];
          let hitBrick = false;

          for (let i = 0; i < newBricks.length; i++) {
            const brick = newBricks[i];
            if (brick.visible &&
                newBall.position.x < brick.x + BRICK_WIDTH &&
                newBall.position.x + BALL_SIZE > brick.x &&
                newBall.position.y < brick.y + BRICK_HEIGHT &&
                newBall.position.y + BALL_SIZE > brick.y) {
              
              newBricks[i] = { ...brick, visible: false };
              newBall.velocity.y = -newBall.velocity.y;
              setScore(prev => prev + 10);
              hitBrick = true;
              break;
            }
          }

          return newBricks;
        });

        // Ball falls below paddle
        if (newBall.position.y > CANVAS_HEIGHT) {
          setLives(prev => {
            const newLives = prev - 1;
            if (newLives <= 0) {
              setGameOver(true);
            } else {
              setTimeout(resetBall, 1000);
            }
            return newLives;
          });
          return prevBall;
        }

        return newBall;
      });
    }, 16); // ~60 FPS

    return () => clearInterval(gameLoop);
  }, [gameStarted, gameOver, gameWon, keys, paddle, resetBall]);

  useEffect(() => {
    const visibleBricks = bricks.filter(brick => brick.visible);
    if (visibleBricks.length === 0 && bricks.length > 0) {
      setGameWon(true);
    }
  }, [bricks]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // Draw bricks
    bricks.forEach(brick => {
      if (brick.visible) {
        ctx.fillStyle = brick.color;
        ctx.fillRect(brick.x, brick.y, BRICK_WIDTH - 2, BRICK_HEIGHT - 2);
      }
    });

    // Draw paddle
    ctx.fillStyle = '#fff';
    ctx.fillRect(paddle, CANVAS_HEIGHT - PADDLE_HEIGHT, PADDLE_WIDTH, PADDLE_HEIGHT);

    // Draw ball
    ctx.fillRect(ball.position.x, ball.position.y, BALL_SIZE, BALL_SIZE);
  }, [paddle, ball, bricks]);

  const startGame = () => {
    setGameStarted(true);
    resetBall();
  };

  const resetGame = () => {
    setScore(0);
    setLives(3);
    setGameStarted(false);
    setGameOver(false);
    setGameWon(false);
    setPaddle(CANVAS_WIDTH / 2 - PADDLE_WIDTH / 2);
    initializeBricks();
    resetBall();
  };

  return (
    <div style={{ textAlign: 'center', padding: '10px' }}>
      <h3 style={{ margin: '0 0 10px 0', color: '#000080' }}>🧱 Breakout</h3>
      
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', fontSize: '11px' }}>
        <div><strong>Score: {score}</strong></div>
        <div><strong>Lives: {lives}</strong></div>
      </div>

      <canvas
        ref={canvasRef}
        width={CANVAS_WIDTH}
        height={CANVAS_HEIGHT}
        style={{
          border: '2px solid #808080',
          background: '#000',
          display: 'block',
          margin: '0 auto 10px'
        }}
      />

      {!gameStarted && !gameOver && !gameWon && (
        <div>
          <button onClick={startGame} style={{
            background: '#c0c0c0', border: '2px outset #c0c0c0', padding: '8px 16px',
            cursor: 'pointer', fontSize: '11px', fontWeight: 'bold'
          }}>
            Start Game
          </button>
          <div style={{ fontSize: '10px', marginTop: '8px' }}>
            Arrow keys or A/D to move paddle
          </div>
        </div>
      )}

      {gameOver && (
        <div>
          <div style={{ color: '#ff0000', fontWeight: 'bold', marginBottom: '8px' }}>
            Game Over! Final Score: {score}
          </div>
          <button onClick={resetGame} style={{
            background: '#c0c0c0', border: '2px outset #c0c0c0', padding: '8px 16px',
            cursor: 'pointer', fontSize: '11px', fontWeight: 'bold'
          }}>
            Play Again
          </button>
        </div>
      )}

      {gameWon && (
        <div>
          <div style={{ color: '#00ff00', fontWeight: 'bold', marginBottom: '8px' }}>
            You Win! Score: {score}
          </div>
          <button onClick={resetGame} style={{
            background: '#c0c0c0', border: '2px outset #c0c0c0', padding: '8px 16px',
            cursor: 'pointer', fontSize: '11px', fontWeight: 'bold'
          }}>
            Play Again
          </button>
        </div>
      )}

      {gameStarted && !gameOver && !gameWon && (
        <div style={{ fontSize: '10px' }}>
          Break all the bricks to win! • Arrow keys or A/D to move
        </div>
      )}
    </div>
  );
};

export default Breakout;
