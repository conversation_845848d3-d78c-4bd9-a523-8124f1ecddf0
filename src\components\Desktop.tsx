import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import Icon from './Icon';
import Window from './Window';
import Taskbar from './Taskbar';
import './Desktop.css';

export interface WindowData {
  id: string;
  title: string;
  content: React.ReactNode;
  isMinimized: boolean;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
}

const Desktop: React.FC = () => {
  const [windows, setWindows] = useState<WindowData[]>([]);
  const [nextZIndex, setNextZIndex] = useState(1000);

  const openWindow = useCallback((id: string, title: string, content: React.ReactNode) => {
    setWindows(prev => {
      // Check if window is already open
      const existingWindow = prev.find(w => w.id === id);
      if (existingWindow) {
        // Bring to front and unminimize
        return prev.map(w => 
          w.id === id 
            ? { ...w, isMinimized: false, zIndex: nextZIndex }
            : w
        );
      }

      // Create new window
      const newWindow: WindowData = {
        id,
        title,
        content,
        isMinimized: false,
        position: { 
          x: 100 + (prev.length * 30), 
          y: 100 + (prev.length * 30) 
        },
        size: { width: 600, height: 400 },
        zIndex: nextZIndex
      };

      setNextZIndex(prev => prev + 1);
      return [...prev, newWindow];
    });
  }, [nextZIndex]);

  const closeWindow = useCallback((id: string) => {
    setWindows(prev => prev.filter(w => w.id !== id));
  }, []);

  const minimizeWindow = useCallback((id: string) => {
    setWindows(prev => 
      prev.map(w => 
        w.id === id ? { ...w, isMinimized: true } : w
      )
    );
  }, []);

  const focusWindow = useCallback((id: string) => {
    setWindows(prev => 
      prev.map(w => 
        w.id === id 
          ? { ...w, zIndex: nextZIndex, isMinimized: false }
          : w
      )
    );
    setNextZIndex(prev => prev + 1);
  }, [nextZIndex]);

  const updateWindowPosition = useCallback((id: string, position: { x: number; y: number }) => {
    setWindows(prev => 
      prev.map(w => 
        w.id === id ? { ...w, position } : w
      )
    );
  }, []);

  const updateWindowSize = useCallback((id: string, size: { width: number; height: number }) => {
    setWindows(prev => 
      prev.map(w => 
        w.id === id ? { ...w, size } : w
      )
    );
  }, []);

  const desktopIcons = [
    {
      id: 'about',
      title: 'About Me',
      icon: '👤',
      content: (
        <div className="window-content">
          <h2>About Me</h2>
          <p>Welcome to my Windows 95-style portfolio!</p>
          <p>I'm a passionate developer who loves creating unique and nostalgic experiences.</p>
          <p>This portfolio showcases my skills in React, TypeScript, and creative UI design.</p>
          <br />
          <p><strong>Skills:</strong></p>
          <ul>
            <li>React & TypeScript</li>
            <li>JavaScript & Node.js</li>
            <li>CSS & SCSS</li>
            <li>UI/UX Design</li>
            <li>Framer Motion</li>
          </ul>
        </div>
      )
    },
    {
      id: 'projects',
      title: 'My Projects',
      icon: '💼',
      content: (
        <div className="window-content">
          <h2>My Projects</h2>
          <div className="project-list">
            <div className="project-item">
              <h3>🎮 Retro Game Collection</h3>
              <p>A collection of classic games built with HTML5 Canvas and JavaScript.</p>
            </div>
            <div className="project-item">
              <h3>📱 Modern Web App</h3>
              <p>A responsive web application built with React and TypeScript.</p>
            </div>
            <div className="project-item">
              <h3>🤖 AI Chat Bot</h3>
              <p>An intelligent chatbot using natural language processing.</p>
            </div>
            <div className="project-item">
              <h3>🌐 Portfolio Website</h3>
              <p>This very website! A nostalgic Windows 95-style portfolio.</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'contact',
      title: 'Contact',
      icon: '📧',
      content: (
        <div className="window-content">
          <h2>Get In Touch</h2>
          <p>I'd love to hear from you! Feel free to reach out through any of these channels:</p>
          <br />
          <div className="contact-info">
            <p><strong>📧 Email:</strong> <EMAIL></p>
            <p><strong>💼 LinkedIn:</strong> linkedin.com/in/yourprofile</p>
            <p><strong>🐙 GitHub:</strong> github.com/yourusername</p>
            <p><strong>🐦 Twitter:</strong> @yourusername</p>
          </div>
          <br />
          <p>Let's build something amazing together!</p>
        </div>
      )
    },
    {
      id: 'computer',
      title: 'My Computer',
      icon: '💻',
      content: (
        <div className="window-content">
          <h2>My Computer</h2>
          <div className="computer-info">
            <div className="drive-item">
              <span className="drive-icon">💾</span>
              <span>Floppy (A:)</span>
            </div>
            <div className="drive-item">
              <span className="drive-icon">💿</span>
              <span>CD-ROM (D:)</span>
            </div>
            <div className="drive-item">
              <span className="drive-icon">🖥️</span>
              <span>Hard Drive (C:)</span>
            </div>
          </div>
          <br />
          <p><strong>System Information:</strong></p>
          <p>Operating System: Windows 95</p>
          <p>Processor: Intel 486 DX2-66</p>
          <p>Memory: 8 MB RAM</p>
          <p>Graphics: VGA Compatible</p>
        </div>
      )
    }
  ];

  return (
    <div className="desktop">
      <div className="desktop-wallpaper" />
      
      <div className="desktop-icons">
        {desktopIcons.map((iconData, index) => (
          <motion.div
            key={iconData.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.3 }}
          >
            <Icon
              title={iconData.title}
              icon={iconData.icon}
              onDoubleClick={() => openWindow(iconData.id, iconData.title, iconData.content)}
            />
          </motion.div>
        ))}
      </div>

      {windows.map(window => (
        !window.isMinimized && (
          <Window
            key={window.id}
            id={window.id}
            title={window.title}
            position={window.position}
            size={window.size}
            zIndex={window.zIndex}
            onClose={() => closeWindow(window.id)}
            onMinimize={() => minimizeWindow(window.id)}
            onFocus={() => focusWindow(window.id)}
            onPositionChange={(position) => updateWindowPosition(window.id, position)}
            onSizeChange={(size) => updateWindowSize(window.id, size)}
          >
            {window.content}
          </Window>
        )
      ))}

      <Taskbar 
        windows={windows}
        onWindowClick={focusWindow}
      />
    </div>
  );
};

export default Desktop;
