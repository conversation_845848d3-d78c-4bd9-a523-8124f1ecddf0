import React, { useState, useEffect } from 'react';
import ProfileCard from './components/ProfileCard';
import Snake from './components/games/Snake';
import Tetris from './components/games/Tetris';
import Pong from './components/games/Pong';
import Breakout from './components/games/Breakout';
import './App.css';

// Simple inline BootScreen component
const SimpleBootScreen = ({ onComplete }: { onComplete: () => void }) => {
  const [stage, setStage] = useState(0);

  useEffect(() => {
    const timer1 = setTimeout(() => setStage(1), 1000);
    const timer2 = setTimeout(() => setStage(2), 2000);
    const timer3 = setTimeout(() => onComplete(), 4000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, [onComplete]);

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      background: '#000',
      color: '#00ff00',
      fontFamily: 'Courier New, monospace',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      fontSize: '14px'
    }}>
      {stage >= 0 && <div>BIOS Version 1.0.0</div>}
      {stage >= 1 && <div>Memory Test: 640K OK</div>}
      {stage >= 2 && (
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <div style={{ color: 'white', fontSize: '24px', marginBottom: '20px' }}>Windows 95</div>
          <div>Loading...</div>
        </div>
      )}
    </div>
  );
};

// Simple Window component
const SimpleWindow = ({ title, children, onClose, position = { x: 100, y: 100 }, size }: {
  title: string;
  children: React.ReactNode;
  onClose: () => void;
  position?: { x: number; y: number };
  size?: { width: string; height: string };
}) => {
  const [windowPosition, setWindowPosition] = useState(position);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - windowPosition.x,
      y: e.clientY - windowPosition.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setWindowPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add event listeners for mouse move and up
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragStart]);

  return (
    <div style={{
      position: 'absolute',
      left: windowPosition.x,
      top: windowPosition.y,
      width: size?.width || '400px',
      height: size?.height || '300px',
      background: '#c0c0c0',
      border: '2px outset #c0c0c0',
      boxShadow: '2px 2px 4px rgba(0,0,0,0.3)',
      fontFamily: 'Arial, sans-serif',
      fontSize: '11px',
      zIndex: 1000,
      userSelect: 'none'
    }}>
      {/* Title Bar */}
      <div
        style={{
          background: 'linear-gradient(90deg, #0000ff 0%, #000080 100%)',
          color: 'white',
          padding: '2px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '18px',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
        onMouseDown={handleMouseDown}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px', paddingLeft: '4px' }}>
          <span>📄</span>
          <span style={{ fontWeight: 'bold' }}>{title}</span>
        </div>
        <button
          onClick={onClose}
          style={{
            width: '16px',
            height: '14px',
            background: '#c0c0c0',
            border: '1px outset #c0c0c0',
            cursor: 'pointer',
            fontSize: '10px',
            fontWeight: 'bold'
          }}
        >
          ×
        </button>
      </div>

      {/* Menu Bar */}
      <div style={{
        background: '#c0c0c0',
        borderBottom: '1px solid #808080',
        display: 'flex',
        padding: '2px 4px',
        gap: '12px',
        height: '16px',
        alignItems: 'center'
      }}>
        <span style={{ padding: '2px 6px', cursor: 'pointer' }}>File</span>
        <span style={{ padding: '2px 6px', cursor: 'pointer' }}>Edit</span>
        <span style={{ padding: '2px 6px', cursor: 'pointer' }}>View</span>
        <span style={{ padding: '2px 6px', cursor: 'pointer' }}>Help</span>
      </div>

      {/* Content */}
      <div style={{
        flex: 1,
        background: 'white',
        border: '1px inset #c0c0c0',
        margin: '2px',
        padding: '8px',
        height: 'calc(100% - 60px)',
        overflow: 'auto'
      }}>
        {children}
      </div>
    </div>
  );
};

// Simple Desktop component
const SimpleDesktop = () => {
  const [openWindows, setOpenWindows] = useState<string[]>([]);
  const [showStartMenu, setShowStartMenu] = useState(false);

  const openWindow = (windowId: string) => {
    if (!openWindows.includes(windowId)) {
      setOpenWindows([...openWindows, windowId]);
    }
  };

  const closeWindow = (windowId: string) => {
    setOpenWindows(openWindows.filter(id => id !== windowId));
  };

  const toggleStartMenu = () => {
    setShowStartMenu(!showStartMenu);
  };

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      background: 'linear-gradient(45deg, #008080 0%, #004040 100%)',
      position: 'relative',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Desktop Icons */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px'
      }}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '64px',
            padding: '4px',
            cursor: 'pointer',
            color: 'white',
            textShadow: '1px 1px 1px black'
          }}
          onDoubleClick={() => openWindow('about')}
        >
          <div style={{
            width: '32px',
            height: '32px',
            background: '#c0c0c0',
            border: '1px outset #c0c0c0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            marginBottom: '2px'
          }}>👤</div>
          <div style={{ fontSize: '11px', textAlign: 'center' }}>About Me</div>
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '64px',
            padding: '4px',
            cursor: 'pointer',
            color: 'white',
            textShadow: '1px 1px 1px black'
          }}
          onDoubleClick={() => openWindow('projects')}
        >
          <div style={{
            width: '32px',
            height: '32px',
            background: '#c0c0c0',
            border: '1px outset #c0c0c0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            marginBottom: '2px'
          }}>💼</div>
          <div style={{ fontSize: '11px', textAlign: 'center' }}>Projects</div>
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '64px',
            padding: '4px',
            cursor: 'pointer',
            color: 'white',
            textShadow: '1px 1px 1px black'
          }}
          onDoubleClick={() => openWindow('contact')}
        >
          <div style={{
            width: '32px',
            height: '32px',
            background: '#c0c0c0',
            border: '1px outset #c0c0c0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            marginBottom: '2px'
          }}>📧</div>
          <div style={{ fontSize: '11px', textAlign: 'center' }}>Contact</div>
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '64px',
            padding: '4px',
            cursor: 'pointer',
            color: 'white',
            textShadow: '1px 1px 1px black'
          }}
          onDoubleClick={() => openWindow('retro-games')}
        >
          <div style={{
            width: '32px',
            height: '32px',
            background: '#c0c0c0',
            border: '1px outset #c0c0c0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            marginBottom: '2px'
          }}>🎮</div>
          <div style={{ fontSize: '11px', textAlign: 'center' }}>Retro Games</div>
        </div>
      </div>

      {/* Taskbar */}
      <div style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        width: '100%',
        height: '28px',
        background: '#c0c0c0',
        borderTop: '1px solid #ffffff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 4px'
      }}>
        <button
          style={{
            height: '22px',
            background: showStartMenu ? '#a0a0a0' : '#c0c0c0',
            border: showStartMenu ? '1px inset #c0c0c0' : '1px outset #c0c0c0',
            fontFamily: 'Arial, sans-serif',
            fontSize: '11px',
            fontWeight: 'bold',
            cursor: 'pointer',
            padding: '0 8px'
          }}
          onClick={toggleStartMenu}
        >
          🪟 Start
        </button>

        <div style={{
          background: '#c0c0c0',
          border: '1px inset #c0c0c0',
          padding: '2px 6px',
          fontSize: '11px'
        }}>
          {new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          })}
        </div>
      </div>

      {/* Start Menu */}
      {showStartMenu && (
        <div style={{
          position: 'fixed',
          bottom: '28px',
          left: '2px',
          width: '200px',
          background: '#c0c0c0',
          border: '2px outset #c0c0c0',
          boxShadow: '2px 2px 4px rgba(0,0,0,0.3)',
          zIndex: 1001,
          fontFamily: 'Arial, sans-serif',
          fontSize: '11px'
        }}>
          <div style={{
            background: 'linear-gradient(90deg, #000080 0%, #0000ff 100%)',
            color: 'white',
            padding: '4px',
            borderBottom: '1px solid #808080'
          }}>
            <div style={{
              writingMode: 'vertical-rl' as any,
              textOrientation: 'mixed' as any,
              height: '120px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '18px',
              fontWeight: 'bold',
              letterSpacing: '2px'
            }}>
              Windows 95
            </div>
          </div>

          <div style={{ padding: '2px 0' }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '4px 8px',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#316AC5'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              onClick={() => {
                openWindow('notepad');
                setShowStartMenu(false);
              }}
            >
              <span style={{ fontSize: '12px', width: '16px', textAlign: 'center' }}>📝</span>
              <span>Notepad</span>
            </div>

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '4px 8px',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#316AC5'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              onClick={() => {
                openWindow('calculator');
                setShowStartMenu(false);
              }}
            >
              <span style={{ fontSize: '12px', width: '16px', textAlign: 'center' }}>🧮</span>
              <span>Calculator</span>
            </div>

            <div style={{
              height: '1px',
              background: '#808080',
              borderBottom: '1px solid #ffffff',
              margin: '2px 8px'
            }} />

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '4px 8px',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#316AC5'}
              onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              onClick={() => setShowStartMenu(false)}
            >
              <span style={{ fontSize: '12px', width: '16px', textAlign: 'center' }}>🔌</span>
              <span>Shut Down...</span>
            </div>
          </div>
        </div>
      )}

      {/* Windows */}
      {openWindows.includes('about') && (
        <SimpleWindow
          title="About Me"
          onClose={() => closeWindow('about')}
          position={{ x: 100, y: 100 }}
          size={{ width: '420px', height: '580px' }}
        >
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '15px',
            height: '100%',
            overflow: 'hidden'
          }}>
            <div style={{
              width: '350px',
              height: '520px',
              transform: 'scale(0.9)',
              transformOrigin: 'center center'
            }}>
              <ProfileCard
                name="Javi A. Torres"
                title="Software Engineer"
                handle="javicodes"
                status="Online"
                contactText="Contact Me"
                avatarUrl="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
                showUserInfo={true}
                enableTilt={true}
                onContactClick={() => openWindow('contact')}
              />
            </div>
          </div>
        </SimpleWindow>
      )}

      {openWindows.includes('projects') && (
        <SimpleWindow
          title="My Projects"
          onClose={() => closeWindow('projects')}
          position={{ x: 150, y: 150 }}
        >
          <div>
            <h2 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold', color: '#000080' }}>
              My Projects
            </h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div
                style={{
                  padding: '8px',
                  border: '1px solid #808080',
                  background: '#f0f0f0',
                  cursor: 'pointer'
                }}
                onClick={() => openWindow('retro-games')}
                onMouseEnter={(e) => e.currentTarget.style.background = '#e0e0e0'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#f0f0f0'}
              >
                <h3 style={{ margin: '0 0 4px 0', fontSize: '12px', fontWeight: 'bold', color: '#000080' }}>
                  🎮 Retro Game Collection
                </h3>
                <p style={{ margin: 0, fontSize: '10px' }}>
                  A collection of classic games built with HTML5 Canvas and JavaScript. Click to view games!
                </p>
              </div>
              <div
                style={{
                  padding: '8px',
                  border: '1px solid #808080',
                  background: '#f0f0f0',
                  cursor: 'pointer'
                }}
                onClick={() => openWindow('web-app')}
                onMouseEnter={(e) => e.currentTarget.style.background = '#e0e0e0'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#f0f0f0'}
              >
                <h3 style={{ margin: '0 0 4px 0', fontSize: '12px', fontWeight: 'bold', color: '#000080' }}>
                  📱 Modern Web App
                </h3>
                <p style={{ margin: 0, fontSize: '10px' }}>
                  A responsive web application built with React and TypeScript. Click to view details!
                </p>
              </div>
              <div
                style={{
                  padding: '8px',
                  border: '1px solid #808080',
                  background: '#f0f0f0',
                  cursor: 'pointer'
                }}
                onClick={() => openWindow('portfolio-website')}
                onMouseEnter={(e) => e.currentTarget.style.background = '#e0e0e0'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#f0f0f0'}
              >
                <h3 style={{ margin: '0 0 4px 0', fontSize: '12px', fontWeight: 'bold', color: '#000080' }}>
                  🌐 Portfolio Website
                </h3>
                <p style={{ margin: 0, fontSize: '10px' }}>
                  This very website! A nostalgic Windows 95-style portfolio. Click to view details!
                </p>
              </div>
            </div>
          </div>
        </SimpleWindow>
      )}

      {openWindows.includes('contact') && (
        <SimpleWindow
          title="Contact"
          onClose={() => closeWindow('contact')}
          position={{ x: 200, y: 200 }}
        >
          <div>
            <h2 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold', color: '#000080' }}>
              Get In Touch
            </h2>
            <p style={{ margin: '0 0 8px 0' }}>
              I'd love to hear from you! Feel free to reach out through any of these channels:
            </p>
            <br />
            <div style={{ fontFamily: 'Courier New, monospace', fontSize: '10px' }}>
              <p style={{ margin: '4px 0' }}><strong>📧 Email:</strong> <EMAIL></p>
              <p style={{ margin: '4px 0' }}><strong>💼 LinkedIn:</strong> linkedin.com/in/yourprofile</p>
              <p style={{ margin: '4px 0' }}><strong>🐙 GitHub:</strong> github.com/yourusername</p>
              <p style={{ margin: '4px 0' }}><strong>🐦 Twitter:</strong> @yourusername</p>
            </div>
            <br />
            <p style={{ margin: 0 }}>Let's build something amazing together!</p>
          </div>
        </SimpleWindow>
      )}

      {openWindows.includes('notepad') && (
        <SimpleWindow
          title="Notepad"
          onClose={() => closeWindow('notepad')}
          position={{ x: 120, y: 80 }}
        >
          <textarea
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              outline: 'none',
              fontFamily: 'Courier New, monospace',
              fontSize: '12px',
              resize: 'none',
              background: 'white',
              color: 'black'
            }}
            defaultValue={`Welcome to Windows 95 Notepad!

This is a simple text editor that mimics the classic Windows 95 Notepad application.

You can type here and experience the nostalgia of the 90s computing era.

Some fun facts about Windows 95:
- Released on August 24, 1995
- Introduced the Start button and taskbar
- First Windows to include Internet Explorer
- Supported long file names (up to 255 characters)

Feel free to edit this text and explore the interface!

---
Created with React + TypeScript
A nostalgic tribute to classic computing`}
          />
        </SimpleWindow>
      )}

      {openWindows.includes('calculator') && (
        <SimpleWindow
          title="Calculator"
          onClose={() => closeWindow('calculator')}
          position={{ x: 250, y: 120 }}
        >
          <div>
            <div style={{ marginBottom: '8px' }}>
              <div style={{
                background: '#000',
                color: '#00ff00',
                fontFamily: 'Courier New, monospace',
                fontSize: '16px',
                padding: '8px',
                textAlign: 'right',
                border: '1px inset #c0c0c0',
                minHeight: '24px'
              }}>
                0
              </div>
            </div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
              {[
                ['C', 'CE', '±', '÷'],
                ['7', '8', '9', '×'],
                ['4', '5', '6', '-'],
                ['1', '2', '3', '+'],
                ['0', '.', '=']
              ].map((row, i) => (
                <div key={i} style={{ display: 'flex', gap: '2px' }}>
                  {row.map((btn, j) => (
                    <button
                      key={j}
                      style={{
                        flex: btn === '0' ? 2 : 1,
                        height: '32px',
                        background: '#c0c0c0',
                        border: '1px outset #c0c0c0',
                        fontFamily: 'Arial, sans-serif',
                        fontSize: '11px',
                        cursor: 'pointer'
                      }}
                      onMouseDown={(e) => e.currentTarget.style.border = '1px inset #c0c0c0'}
                      onMouseUp={(e) => e.currentTarget.style.border = '1px outset #c0c0c0'}
                      onMouseLeave={(e) => e.currentTarget.style.border = '1px outset #c0c0c0'}
                    >
                      {btn}
                    </button>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </SimpleWindow>
      )}

      {openWindows.includes('retro-games') && (
        <SimpleWindow
          title="Retro Game Collection"
          onClose={() => closeWindow('retro-games')}
          position={{ x: 300, y: 150 }}
        >
          <div>
            <h2 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold', color: '#000080' }}>
              🎮 Classic Games Collection
            </h2>
            <p style={{ margin: '0 0 16px 0', fontSize: '11px', lineHeight: '1.4' }}>
              A nostalgic collection of retro games built with modern web technologies.
              Click on any game to play!
            </p>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {[
                { name: 'Snake', icon: '🐍', description: 'Classic Nokia snake game with modern controls', id: 'snake' },
                { name: 'Tetris', icon: '🧩', description: 'The legendary block-stacking puzzle game', id: 'tetris' },
                { name: 'Pong', icon: '🏓', description: 'The original arcade tennis simulation', id: 'pong' },
                { name: 'Breakout', icon: '🧱', description: 'Break bricks with a bouncing ball and paddle', id: 'breakout' },
                { name: 'Pac-Man', icon: '👻', description: 'Navigate mazes and eat dots while avoiding ghosts', id: 'coming-soon' },
                { name: 'Space Invaders', icon: '👾', description: 'Defend Earth from waves of alien invaders', id: 'coming-soon' },
                { name: 'Asteroids', icon: '☄️', description: 'Pilot a spaceship through an asteroid field', id: 'coming-soon' },
                { name: 'Frogger', icon: '🐸', description: 'Help the frog cross busy roads and rivers', id: 'coming-soon' }
              ].map((game, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    padding: '8px',
                    border: '1px solid #808080',
                    background: '#f0f0f0',
                    cursor: 'pointer',
                    fontSize: '11px'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#316AC5';
                    e.currentTarget.style.color = 'white';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = '#f0f0f0';
                    e.currentTarget.style.color = 'black';
                  }}
                  onClick={() => {
                    if (game.id === 'coming-soon') {
                      alert(`🎮 ${game.name} - Coming Soon!\n\nThis game is currently in development. Check back later for the full retro gaming experience!`);
                    } else {
                      openWindow(`game-${game.id}`);
                    }
                  }}
                >
                  <div style={{
                    fontSize: '20px',
                    width: '32px',
                    textAlign: 'center',
                    flexShrink: 0
                  }}>
                    {game.icon}
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
                      {game.name}
                    </div>
                    <div style={{ fontSize: '10px', opacity: 0.8 }}>
                      {game.description}
                    </div>
                  </div>
                  <div style={{
                    fontSize: '10px',
                    color: '#008000',
                    fontWeight: 'bold'
                  }}>
                    PLAY ▶
                  </div>
                </div>
              ))}
            </div>

            <div style={{
              marginTop: '16px',
              padding: '8px',
              background: '#ffffcc',
              border: '1px solid #cccc00',
              fontSize: '10px',
              lineHeight: '1.4'
            }}>
              <strong>💡 Tech Stack:</strong> HTML5 Canvas, JavaScript ES6+, CSS3 Animations
              <br />
              <strong>🎯 Features:</strong> Responsive controls, high scores, retro sound effects
            </div>
          </div>
        </SimpleWindow>
      )}

      {/* Game Windows */}
      {openWindows.includes('game-snake') && (
        <SimpleWindow
          title="🐍 Snake Game"
          onClose={() => closeWindow('game-snake')}
          position={{ x: 150, y: 100 }}
          size={{ width: '450px', height: '550px' }}
        >
          <Snake />
        </SimpleWindow>
      )}

      {openWindows.includes('game-tetris') && (
        <SimpleWindow
          title="🧩 Tetris"
          onClose={() => closeWindow('game-tetris')}
          position={{ x: 200, y: 120 }}
          size={{ width: '300px', height: '500px' }}
        >
          <Tetris />
        </SimpleWindow>
      )}

      {openWindows.includes('game-pong') && (
        <SimpleWindow
          title="🏓 Pong"
          onClose={() => closeWindow('game-pong')}
          position={{ x: 250, y: 140 }}
          size={{ width: '450px', height: '420px' }}
        >
          <Pong />
        </SimpleWindow>
      )}

      {openWindows.includes('game-breakout') && (
        <SimpleWindow
          title="🧱 Breakout"
          onClose={() => closeWindow('game-breakout')}
          position={{ x: 180, y: 160 }}
          size={{ width: '450px', height: '420px' }}
        >
          <Breakout />
        </SimpleWindow>
      )}

      {/* Project Detail Windows */}
      {openWindows.includes('web-app') && (
        <SimpleWindow
          title="📱 Modern Web App"
          onClose={() => closeWindow('web-app')}
          position={{ x: 200, y: 100 }}
          size={{ width: '500px', height: '400px' }}
        >
          <div style={{ padding: '10px' }}>
            <h2 style={{ margin: '0 0 15px 0', fontSize: '16px', fontWeight: 'bold', color: '#000080' }}>
              📱 Modern Web Application
            </h2>

            <div style={{ marginBottom: '15px' }}>
              <img
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=200&fit=crop"
                alt="Modern Web App Screenshot"
                style={{
                  width: '100%',
                  height: '150px',
                  objectFit: 'cover',
                  border: '2px inset #c0c0c0',
                  marginBottom: '10px'
                }}
              />
            </div>

            <div style={{ fontSize: '11px', lineHeight: '1.4' }}>
              <p><strong>Project Overview:</strong></p>
              <p>A full-stack web application built with modern technologies, featuring responsive design, real-time updates, and seamless user experience.</p>

              <p><strong>🛠️ Tech Stack:</strong></p>
              <ul style={{ margin: '5px 0 10px 20px', padding: 0 }}>
                <li>Frontend: React 18, TypeScript, Tailwind CSS</li>
                <li>Backend: Node.js, Express, PostgreSQL</li>
                <li>Deployment: Vercel, Railway</li>
                <li>Features: Authentication, Real-time chat, File uploads</li>
              </ul>

              <p><strong>🎯 Key Features:</strong></p>
              <ul style={{ margin: '5px 0 10px 20px', padding: 0 }}>
                <li>Responsive design for all devices</li>
                <li>Real-time notifications and updates</li>
                <li>Secure user authentication</li>
                <li>File upload and management system</li>
                <li>Advanced search and filtering</li>
              </ul>

              <div style={{
                background: '#ffffcc',
                border: '1px solid #cccc00',
                padding: '8px',
                marginTop: '10px',
                fontSize: '10px'
              }}>
                <strong>🚀 Status:</strong> Live in production • <strong>👥 Users:</strong> 1,000+ active users
              </div>
            </div>
          </div>
        </SimpleWindow>
      )}

      {openWindows.includes('portfolio-website') && (
        <SimpleWindow
          title="🌐 Portfolio Website"
          onClose={() => closeWindow('portfolio-website')}
          position={{ x: 250, y: 120 }}
          size={{ width: '500px', height: '400px' }}
        >
          <div style={{ padding: '10px' }}>
            <h2 style={{ margin: '0 0 15px 0', fontSize: '16px', fontWeight: 'bold', color: '#000080' }}>
              🌐 Windows 95 Portfolio Website
            </h2>

            <div style={{ marginBottom: '15px' }}>
              <div style={{
                width: '100%',
                height: '150px',
                background: 'linear-gradient(45deg, #008080 0%, #004040 100%)',
                border: '2px inset #c0c0c0',
                marginBottom: '10px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '24px',
                fontWeight: 'bold'
              }}>
                🪟 Windows 95 Portfolio
              </div>
            </div>

            <div style={{ fontSize: '11px', lineHeight: '1.4' }}>
              <p><strong>Project Overview:</strong></p>
              <p>This very website! A nostalgic recreation of the Windows 95 interface built with modern React and TypeScript, featuring interactive games, draggable windows, and authentic retro styling.</p>

              <p><strong>🛠️ Tech Stack:</strong></p>
              <ul style={{ margin: '5px 0 10px 20px', padding: 0 }}>
                <li>Frontend: React 18, TypeScript, Framer Motion</li>
                <li>Games: HTML5 Canvas, Custom game engines</li>
                <li>Styling: Custom CSS with Windows 95 design system</li>
                <li>Interactions: react-draggable, Web Audio API</li>
              </ul>

              <p><strong>🎯 Features Implemented:</strong></p>
              <ul style={{ margin: '5px 0 10px 20px', padding: 0 }}>
                <li>Authentic Windows 95 boot sequence</li>
                <li>Draggable, resizable windows</li>
                <li>Functional retro games (Snake, Tetris, Pong, Breakout)</li>
                <li>Interactive desktop with right-click menus</li>
                <li>Modern ProfileCard with 3D tilt effects</li>
                <li>Retro sound effects using Web Audio API</li>
              </ul>

              <div style={{
                background: '#ffffcc',
                border: '1px solid #cccc00',
                padding: '8px',
                marginTop: '10px',
                fontSize: '10px'
              }}>
                <strong>🎨 Design Philosophy:</strong> Blending 90s nostalgia with modern web development techniques
              </div>
            </div>
          </div>
        </SimpleWindow>
      )}
    </div>
  );
};

function App() {
  const [isBooted, setIsBooted] = useState(false);

  const handleBootComplete = () => {
    setIsBooted(true);
  };

  return (
    <div className="app">
      {!isBooted ? (
        <SimpleBootScreen onComplete={handleBootComplete} />
      ) : (
        <SimpleDesktop />
      )}
    </div>
  );
}

export default App;
